# -*- coding: utf-8 -*-
"""
واجهة الإعدادات والتهيئة
Settings Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from datetime import datetime
import os
from pathlib import Path

class SettingsManagement:
    """فئة إدارة الإعدادات والتهيئة"""

    def __init__(self, parent_frame, db_manager):
        """تهيئة واجهة الإعدادات"""
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.main_frame = None
        self.current_view = "institutions"

        self.create_interface()

    def create_interface(self):
        """إنشاء واجهة الإعدادات"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()

        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.parent_frame,
            text="الإعدادات والتهيئة",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)

        # أزرار التبديل
        self.create_tab_buttons()

        # المنطقة الرئيسية
        self.main_frame = ctk.CTkFrame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # عرض بيانات المؤسسات افتراضياً
        self.show_institutions()

    def create_tab_buttons(self):
        """إنشاء أزرار التبديل بين الأقسام"""
        tab_frame = ctk.CTkFrame(self.parent_frame)
        tab_frame.pack(pady=10)

        # عنوان البيانات الأساسية
        basic_data_label = ctk.CTkLabel(
            tab_frame,
            text="البيانات الأساسية",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        basic_data_label.pack(pady=(5, 0))

        # الصف الأول من الأزرار - البيانات الأساسية
        row1_frame = ctk.CTkFrame(tab_frame)
        row1_frame.pack(pady=5)

        self.institutions_btn = ctk.CTkButton(
            row1_frame,
            text="بيانات المؤسسة",
            command=self.show_institutions,
            width=110
        )
        self.institutions_btn.pack(side="left", padx=2)

        self.departments_btn = ctk.CTkButton(
            row1_frame,
            text="بيانات الدوائر",
            command=self.show_departments,
            width=110
        )
        self.departments_btn.pack(side="left", padx=2)

        self.currencies_btn = ctk.CTkButton(
            row1_frame,
            text="دليل العملات",
            command=self.show_currencies,
            width=110
        )
        self.currencies_btn.pack(side="left", padx=2)

        self.accounts_btn = ctk.CTkButton(
            row1_frame,
            text="الحسابات الختامية",
            command=self.show_final_accounts,
            width=110
        )
        self.accounts_btn.pack(side="left", padx=2)

        self.periods_btn = ctk.CTkButton(
            row1_frame,
            text="الفترة المحاسبية",
            command=self.show_accounting_periods,
            width=110
        )
        self.periods_btn.pack(side="left", padx=2)

        # عنوان إدارة قواعد البيانات
        db_management_label = ctk.CTkLabel(
            tab_frame,
            text="إدارة قواعد البيانات",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        db_management_label.pack(pady=(10, 0))

        # الصف الثاني من الأزرار - إدارة قواعد البيانات
        row2_frame = ctk.CTkFrame(tab_frame)
        row2_frame.pack(pady=5)

        self.database_btn = ctk.CTkButton(
            row2_frame,
            text="إدارة قواعد البيانات",
            command=self.show_database_management,
            width=140
        )
        self.database_btn.pack(side="left", padx=3)

        self.backup_btn = ctk.CTkButton(
            row2_frame,
            text="النسخ الاحتياطي",
            command=self.show_backup_management,
            width=120
        )
        self.backup_btn.pack(side="left", padx=3)

        self.closure_btn = ctk.CTkButton(
            row2_frame,
            text="إقفال قواعد البيانات",
            command=self.show_database_closure,
            width=140
        )
        self.closure_btn.pack(side="left", padx=3)

        self.new_db_btn = ctk.CTkButton(
            row2_frame,
            text="إنشاء قاعدة بيانات جديدة",
            command=self.show_new_database,
            width=160
        )
        self.new_db_btn.pack(side="left", padx=3)

    def show_institutions(self):
        """عرض بيانات المؤسسات"""
        self.current_view = "institutions"
        self.clear_main_frame()
        self.highlight_button(self.institutions_btn)

        # عنوان القسم
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="بيانات المؤسسة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إضافة مؤسسة جديدة",
            command=self.add_institution_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="تعديل المؤسسة",
            command=self.edit_institution_dialog,
            width=150
        ).pack(side="left", padx=5)

        # جدول المؤسسات
        self.create_institutions_table()
        self.load_institutions_data()

    def create_institutions_table(self):
        """إنشاء جدول المؤسسات"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("رقم المؤسسة", "اسم المؤسسة", "العنوان", "البريد الإلكتروني", "الموقع الإلكتروني")
        self.institutions_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.institutions_tree.heading(col, text=col)
            self.institutions_tree.column(col, width=150, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.institutions_tree.yview)
        self.institutions_tree.configure(yscrollcommand=scrollbar.set)

        self.institutions_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_institutions_data(self):
        """تحميل بيانات المؤسسات"""
        try:
            for item in self.institutions_tree.get_children():
                self.institutions_tree.delete(item)

            institutions = self.db_manager.fetch_all("""
                SELECT institution_number, name, address, email, website
                FROM institutions
                ORDER BY institution_number
            """)

            for inst in institutions:
                self.institutions_tree.insert("", "end", values=(
                    inst['institution_number'],
                    inst['name'],
                    inst['address'] or "",
                    inst['email'] or "",
                    inst['website'] or ""
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المؤسسات:\n{str(e)}")

    def add_institution_dialog(self):
        """حوار إضافة مؤسسة جديدة"""
        dialog = ctk.CTkToplevel()
        dialog.title("إضافة مؤسسة جديدة")
        dialog.geometry("500x600")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"500x600+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text="إضافة مؤسسة جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(dialog, width=450, height=450)
        scroll_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # حقول الإدخال
        fields = {}

        # رقم المؤسسة
        ctk.CTkLabel(scroll_frame, text="رقم المؤسسة:").pack(anchor="w", pady=5)
        fields['institution_number'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['institution_number'].pack(pady=5)

        # اسم المؤسسة
        ctk.CTkLabel(scroll_frame, text="اسم المؤسسة:").pack(anchor="w", pady=5)
        fields['name'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['name'].pack(pady=5)

        # العنوان
        ctk.CTkLabel(scroll_frame, text="العنوان:").pack(anchor="w", pady=5)
        fields['address'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['address'].pack(pady=5)

        # البريد الإلكتروني
        ctk.CTkLabel(scroll_frame, text="البريد الإلكتروني:").pack(anchor="w", pady=5)
        fields['email'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['email'].pack(pady=5)

        # الموقع الإلكتروني
        ctk.CTkLabel(scroll_frame, text="الموقع الإلكتروني:").pack(anchor="w", pady=5)
        fields['website'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['website'].pack(pady=5)

        # الملاحظات
        ctk.CTkLabel(scroll_frame, text="الملاحظات:").pack(anchor="w", pady=5)
        fields['notes'] = ctk.CTkTextbox(scroll_frame, width=400, height=80)
        fields['notes'].pack(pady=5)

        # الشعار
        ctk.CTkLabel(scroll_frame, text="شعار المؤسسة:").pack(anchor="w", pady=5)
        logo_frame = ctk.CTkFrame(scroll_frame)
        logo_frame.pack(pady=5, fill="x")

        fields['logo_path'] = ctk.CTkEntry(logo_frame, width=300)
        fields['logo_path'].pack(side="left", padx=5)

        def browse_logo():
            filename = filedialog.askopenfilename(
                title="اختر شعار المؤسسة",
                filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp")]
            )
            if filename:
                fields['logo_path'].delete(0, "end")
                fields['logo_path'].insert(0, filename)

        ctk.CTkButton(
            logo_frame,
            text="تصفح",
            command=browse_logo,
            width=80
        ).pack(side="right", padx=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def save_institution():
            """حفظ المؤسسة الجديدة"""
            institution_number = fields['institution_number'].get().strip()
            name = fields['name'].get().strip()
            address = fields['address'].get().strip()
            email = fields['email'].get().strip()
            website = fields['website'].get().strip()
            notes = fields['notes'].get("1.0", "end-1c").strip()
            logo_path = fields['logo_path'].get().strip()

            if not institution_number or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة (رقم المؤسسة واسم المؤسسة)")
                return

            try:
                self.db_manager.execute_query("""
                    INSERT INTO institutions
                    (institution_number, name, address, email, website, notes, logo_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (institution_number, name, address, email, website, notes, logo_path))

                messagebox.showinfo("نجح", "تم إضافة المؤسسة بنجاح")
                dialog.destroy()
                self.load_institutions_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المؤسسة:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_institution,
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def edit_institution_dialog(self):
        """حوار تعديل المؤسسة"""
        selected = self.institutions_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مؤسسة للتعديل")
            return

        # جلب بيانات المؤسسة المحددة
        item = self.institutions_tree.item(selected[0])
        values = item['values']

        # جلب البيانات الكاملة من قاعدة البيانات
        institution_data = self.db_manager.fetch_one("""
            SELECT * FROM institutions WHERE institution_number = ?
        """, (values[0],))

        if not institution_data:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات المؤسسة")
            return

        dialog = ctk.CTkToplevel()
        dialog.title("تعديل المؤسسة")
        dialog.geometry("500x600")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"500x600+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text="تعديل المؤسسة",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(dialog, width=450, height=450)
        scroll_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # حقول الإدخال مع البيانات الحالية
        fields = {}

        # رقم المؤسسة
        ctk.CTkLabel(scroll_frame, text="رقم المؤسسة:").pack(anchor="w", pady=5)
        fields['institution_number'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['institution_number'].pack(pady=5)
        fields['institution_number'].insert(0, institution_data['institution_number'])

        # اسم المؤسسة
        ctk.CTkLabel(scroll_frame, text="اسم المؤسسة:").pack(anchor="w", pady=5)
        fields['name'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['name'].pack(pady=5)
        fields['name'].insert(0, institution_data['name'])

        # العنوان
        ctk.CTkLabel(scroll_frame, text="العنوان:").pack(anchor="w", pady=5)
        fields['address'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['address'].pack(pady=5)
        if institution_data['address']:
            fields['address'].insert(0, institution_data['address'])

        # البريد الإلكتروني
        ctk.CTkLabel(scroll_frame, text="البريد الإلكتروني:").pack(anchor="w", pady=5)
        fields['email'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['email'].pack(pady=5)
        if institution_data['email']:
            fields['email'].insert(0, institution_data['email'])

        # الموقع الإلكتروني
        ctk.CTkLabel(scroll_frame, text="الموقع الإلكتروني:").pack(anchor="w", pady=5)
        fields['website'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['website'].pack(pady=5)
        if institution_data['website']:
            fields['website'].insert(0, institution_data['website'])

        # الملاحظات
        ctk.CTkLabel(scroll_frame, text="الملاحظات:").pack(anchor="w", pady=5)
        fields['notes'] = ctk.CTkTextbox(scroll_frame, width=400, height=80)
        fields['notes'].pack(pady=5)
        if institution_data['notes']:
            fields['notes'].insert("1.0", institution_data['notes'])

        # الشعار
        ctk.CTkLabel(scroll_frame, text="شعار المؤسسة:").pack(anchor="w", pady=5)
        logo_frame = ctk.CTkFrame(scroll_frame)
        logo_frame.pack(pady=5, fill="x")

        fields['logo_path'] = ctk.CTkEntry(logo_frame, width=300)
        fields['logo_path'].pack(side="left", padx=5)
        if institution_data['logo_path']:
            fields['logo_path'].insert(0, institution_data['logo_path'])

        def browse_logo():
            filename = filedialog.askopenfilename(
                title="اختر شعار المؤسسة",
                filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp")]
            )
            if filename:
                fields['logo_path'].delete(0, "end")
                fields['logo_path'].insert(0, filename)

        ctk.CTkButton(
            logo_frame,
            text="تصفح",
            command=browse_logo,
            width=80
        ).pack(side="right", padx=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def update_institution():
            """تحديث المؤسسة"""
            new_institution_number = fields['institution_number'].get().strip()
            new_name = fields['name'].get().strip()
            new_address = fields['address'].get().strip()
            new_email = fields['email'].get().strip()
            new_website = fields['website'].get().strip()
            new_notes = fields['notes'].get("1.0", "end-1c").strip()
            new_logo_path = fields['logo_path'].get().strip()

            if not new_institution_number or not new_name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            try:
                self.db_manager.execute_query("""
                    UPDATE institutions
                    SET institution_number = ?, name = ?, address = ?, email = ?,
                        website = ?, notes = ?, logo_path = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (new_institution_number, new_name, new_address, new_email,
                      new_website, new_notes, new_logo_path, institution_data['id']))

                messagebox.showinfo("نجح", "تم تحديث المؤسسة بنجاح")
                dialog.destroy()
                self.load_institutions_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث المؤسسة:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="تحديث",
            command=update_institution,
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def show_departments(self):
        """عرض بيانات الدوائر"""
        self.current_view = "departments"
        self.clear_main_frame()
        self.highlight_button(self.departments_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="بيانات الدوائر",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إضافة دائرة جديدة",
            command=self.add_department_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="تعديل الدائرة",
            command=self.edit_department_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="حذف الدائرة",
            command=self.delete_department,
            width=150
        ).pack(side="left", padx=5)

        # جدول الدوائر
        self.create_departments_table()
        self.load_departments_data()

    def create_departments_table(self):
        """إنشاء جدول الدوائر"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("رقم الدائرة", "اسم المؤسسة", "اسم الدائرة", "العنوان", "رقم الهاتف", "البريد الإلكتروني")
        self.departments_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.departments_tree.heading(col, text=col)
            self.departments_tree.column(col, width=120, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.departments_tree.yview)
        self.departments_tree.configure(yscrollcommand=scrollbar.set)

        self.departments_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_departments_data(self):
        """تحميل بيانات الدوائر"""
        try:
            for item in self.departments_tree.get_children():
                self.departments_tree.delete(item)

            departments = self.db_manager.fetch_all("""
                SELECT d.department_number, i.name as institution_name, d.name, d.address, d.phone, d.email
                FROM departments d
                LEFT JOIN institutions i ON d.institution_id = i.id
                ORDER BY d.department_number
            """)

            for dept in departments:
                self.departments_tree.insert("", "end", values=(
                    dept['department_number'],
                    dept['institution_name'] or "",
                    dept['name'],
                    dept['address'] or "",
                    dept['phone'] or "",
                    dept['email'] or ""
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الدوائر:\n{str(e)}")

    def add_department_dialog(self):
        """حوار إضافة دائرة جديدة"""
        dialog = ctk.CTkToplevel()
        dialog.title("إضافة دائرة جديدة")
        dialog.geometry("500x500")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"500x500+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text="إضافة دائرة جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(dialog, width=450, height=350)
        scroll_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # حقول الإدخال
        fields = {}

        # رقم الدائرة
        ctk.CTkLabel(scroll_frame, text="رقم الدائرة:").pack(anchor="w", pady=5)
        fields['department_number'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['department_number'].pack(pady=5)

        # اسم المؤسسة (قائمة منسدلة)
        ctk.CTkLabel(scroll_frame, text="اسم المؤسسة:").pack(anchor="w", pady=5)
        institutions = self.get_institutions_list()
        fields['institution'] = ctk.CTkComboBox(scroll_frame, values=institutions, width=400)
        fields['institution'].pack(pady=5)

        # اسم الدائرة
        ctk.CTkLabel(scroll_frame, text="اسم الدائرة:").pack(anchor="w", pady=5)
        fields['name'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['name'].pack(pady=5)

        # العنوان
        ctk.CTkLabel(scroll_frame, text="العنوان:").pack(anchor="w", pady=5)
        fields['address'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['address'].pack(pady=5)

        # رقم الهاتف
        ctk.CTkLabel(scroll_frame, text="رقم الهاتف:").pack(anchor="w", pady=5)
        fields['phone'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['phone'].pack(pady=5)

        # البريد الإلكتروني
        ctk.CTkLabel(scroll_frame, text="البريد الإلكتروني:").pack(anchor="w", pady=5)
        fields['email'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['email'].pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def save_department():
            """حفظ الدائرة الجديدة"""
            department_number = fields['department_number'].get().strip()
            institution_name = fields['institution'].get()
            name = fields['name'].get().strip()
            address = fields['address'].get().strip()
            phone = fields['phone'].get().strip()
            email = fields['email'].get().strip()

            if not department_number or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة (رقم الدائرة واسم الدائرة)")
                return

            try:
                # جلب معرف المؤسسة
                institution_id = None
                if institution_name:
                    inst = self.db_manager.fetch_one("""
                        SELECT id FROM institutions WHERE name = ?
                    """, (institution_name,))
                    if inst:
                        institution_id = inst['id']

                self.db_manager.execute_query("""
                    INSERT INTO departments
                    (department_number, institution_id, name, address, phone, email)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (department_number, institution_id, name, address, phone, email))

                messagebox.showinfo("نجح", "تم إضافة الدائرة بنجاح")
                dialog.destroy()
                self.load_departments_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الدائرة:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_department,
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def get_institutions_list(self):
        """الحصول على قائمة المؤسسات"""
        try:
            institutions = self.db_manager.fetch_all("SELECT name FROM institutions ORDER BY name")
            return [inst['name'] for inst in institutions]
        except:
            return []

    def edit_department_dialog(self):
        """حوار تعديل الدائرة"""
        selected = self.departments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار دائرة للتعديل")
            return

        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def delete_department(self):
        """حذف الدائرة"""
        selected = self.departments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار دائرة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الدائرة؟")
        if not result:
            return

        # جلب رقم الدائرة
        item = self.departments_tree.item(selected[0])
        department_number = item['values'][0]

        try:
            self.db_manager.execute_query("""
                DELETE FROM departments WHERE department_number = ?
            """, (department_number,))

            messagebox.showinfo("نجح", "تم حذف الدائرة بنجاح")
            self.load_departments_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف الدائرة:\n{str(e)}")

    def show_currencies(self):
        """عرض دليل العملات"""
        self.current_view = "currencies"
        self.clear_main_frame()
        self.highlight_button(self.currencies_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="دليل العملات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إضافة عملة جديدة",
            command=self.add_currency_dialog,
            width=150
        ).pack(side="left", padx=5)

        # جدول العملات
        self.create_currencies_table()
        self.load_currencies_data()

    def create_currencies_table(self):
        """إنشاء جدول العملات"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("رقم العملة", "اسم العملة", "رمز العملة", "أجزاء العملة", "نوع العملة")
        self.currencies_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.currencies_tree.heading(col, text=col)
            self.currencies_tree.column(col, width=120, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.currencies_tree.yview)
        self.currencies_tree.configure(yscrollcommand=scrollbar.set)

        self.currencies_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_currencies_data(self):
        """تحميل بيانات العملات"""
        try:
            for item in self.currencies_tree.get_children():
                self.currencies_tree.delete(item)

            currencies = self.db_manager.fetch_all("""
                SELECT currency_number, name, symbol, parts, currency_type
                FROM currencies
                ORDER BY currency_number
            """)

            for currency in currencies:
                self.currencies_tree.insert("", "end", values=(
                    currency['currency_number'],
                    currency['name'],
                    currency['symbol'],
                    currency['parts'] or "",
                    currency['currency_type']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات العملات:\n{str(e)}")

    def add_currency_dialog(self):
        """حوار إضافة عملة جديدة"""
        dialog = ctk.CTkToplevel()
        dialog.title("إضافة عملة جديدة")
        dialog.geometry("400x400")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"400x400+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text="إضافة عملة جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(pady=20, padx=20, fill="both", expand=True)

        fields = {}

        # رقم العملة
        ctk.CTkLabel(fields_frame, text="رقم العملة:").pack(anchor="w", pady=5)
        fields['currency_number'] = ctk.CTkEntry(fields_frame, width=300)
        fields['currency_number'].pack(pady=5)

        # اسم العملة
        ctk.CTkLabel(fields_frame, text="اسم العملة:").pack(anchor="w", pady=5)
        fields['name'] = ctk.CTkEntry(fields_frame, width=300)
        fields['name'].pack(pady=5)

        # رمز العملة
        ctk.CTkLabel(fields_frame, text="رمز العملة:").pack(anchor="w", pady=5)
        fields['symbol'] = ctk.CTkEntry(fields_frame, width=300)
        fields['symbol'].pack(pady=5)

        # أجزاء العملة
        ctk.CTkLabel(fields_frame, text="أجزاء العملة:").pack(anchor="w", pady=5)
        fields['parts'] = ctk.CTkEntry(fields_frame, width=300)
        fields['parts'].pack(pady=5)

        # نوع العملة
        ctk.CTkLabel(fields_frame, text="نوع العملة:").pack(anchor="w", pady=5)
        fields['currency_type'] = ctk.CTkComboBox(
            fields_frame,
            values=["محلية", "أجنبية"],
            width=300
        )
        fields['currency_type'].pack(pady=5)
        fields['currency_type'].set("محلية")

        # الملاحظات
        ctk.CTkLabel(fields_frame, text="الملاحظات:").pack(anchor="w", pady=5)
        fields['notes'] = ctk.CTkTextbox(fields_frame, width=300, height=60)
        fields['notes'].pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def save_currency():
            """حفظ العملة الجديدة"""
            currency_number = fields['currency_number'].get().strip()
            name = fields['name'].get().strip()
            symbol = fields['symbol'].get().strip()
            parts = fields['parts'].get().strip()
            currency_type = fields['currency_type'].get()
            notes = fields['notes'].get("1.0", "end-1c").strip()

            if not currency_number or not name or not symbol:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            try:
                self.db_manager.execute_query("""
                    INSERT INTO currencies (currency_number, name, symbol, parts, currency_type, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (currency_number, name, symbol, parts, currency_type, notes))

                messagebox.showinfo("نجح", "تم إضافة العملة بنجاح")
                dialog.destroy()
                self.load_currencies_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة العملة:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_currency,
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def show_final_accounts(self):
        """عرض الحسابات الختامية"""
        self.current_view = "final_accounts"
        self.clear_main_frame()
        self.highlight_button(self.accounts_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="الحسابات الختامية",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إضافة حساب ختامي",
            command=self.add_final_account_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="تعديل الحساب",
            command=self.edit_final_account_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="حذف الحساب",
            command=self.delete_final_account,
            width=150
        ).pack(side="left", padx=5)

        # جدول الحسابات الختامية
        self.create_final_accounts_table()
        self.load_final_accounts_data()

    def create_final_accounts_table(self):
        """إنشاء جدول الحسابات الختامية"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("رقم الحساب", "اسم الحساب", "الملاحظات")
        self.final_accounts_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.final_accounts_tree.heading(col, text=col)
            self.final_accounts_tree.column(col, width=200, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.final_accounts_tree.yview)
        self.final_accounts_tree.configure(yscrollcommand=scrollbar.set)

        self.final_accounts_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_final_accounts_data(self):
        """تحميل بيانات الحسابات الختامية"""
        try:
            for item in self.final_accounts_tree.get_children():
                self.final_accounts_tree.delete(item)

            accounts = self.db_manager.fetch_all("""
                SELECT account_number, account_name, notes
                FROM chart_of_accounts
                WHERE account_type = 'ختامي'
                ORDER BY account_number
            """)

            for account in accounts:
                self.final_accounts_tree.insert("", "end", values=(
                    account['account_number'],
                    account['account_name'],
                    account['notes'] or ""
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الحسابات الختامية:\n{str(e)}")

    def add_final_account_dialog(self):
        """حوار إضافة حساب ختامي جديد"""
        dialog = ctk.CTkToplevel()
        dialog.title("إضافة حساب ختامي جديد")
        dialog.geometry("400x350")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (350 // 2)
        dialog.geometry(f"400x350+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text="إضافة حساب ختامي جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(pady=20, padx=20, fill="both", expand=True)

        fields = {}

        # رقم الحساب
        ctk.CTkLabel(fields_frame, text="رقم الحساب:").pack(anchor="w", pady=5)
        fields['account_number'] = ctk.CTkEntry(fields_frame, width=300)
        fields['account_number'].pack(pady=5)

        # اسم الحساب
        ctk.CTkLabel(fields_frame, text="اسم الحساب:").pack(anchor="w", pady=5)
        fields['account_name'] = ctk.CTkEntry(fields_frame, width=300)
        fields['account_name'].pack(pady=5)

        # الملاحظات
        ctk.CTkLabel(fields_frame, text="الملاحظات:").pack(anchor="w", pady=5)
        fields['notes'] = ctk.CTkTextbox(fields_frame, width=300, height=80)
        fields['notes'].pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def save_final_account():
            """حفظ الحساب الختامي الجديد"""
            account_number = fields['account_number'].get().strip()
            account_name = fields['account_name'].get().strip()
            notes = fields['notes'].get("1.0", "end-1c").strip()

            if not account_number or not account_name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            try:
                self.db_manager.execute_query("""
                    INSERT INTO chart_of_accounts (account_number, account_name, account_type, notes)
                    VALUES (?, ?, ?, ?)
                """, (account_number, account_name, "ختامي", notes))

                messagebox.showinfo("نجح", "تم إضافة الحساب الختامي بنجاح")
                dialog.destroy()
                self.load_final_accounts_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الحساب الختامي:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_final_account,
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def edit_final_account_dialog(self):
        """حوار تعديل الحساب الختامي"""
        selected = self.final_accounts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب للتعديل")
            return

        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def delete_final_account(self):
        """حذف الحساب الختامي"""
        selected = self.final_accounts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الحساب؟")
        if not result:
            return

        # جلب رقم الحساب
        item = self.final_accounts_tree.item(selected[0])
        account_number = item['values'][0]

        try:
            self.db_manager.execute_query("""
                DELETE FROM chart_of_accounts WHERE account_number = ? AND account_type = 'ختامي'
            """, (account_number,))

            messagebox.showinfo("نجح", "تم حذف الحساب الختامي بنجاح")
            self.load_final_accounts_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف الحساب الختامي:\n{str(e)}")

    def show_accounting_periods(self):
        """عرض الفترات المحاسبية"""
        self.current_view = "accounting_periods"
        self.clear_main_frame()
        self.highlight_button(self.periods_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="الفترة المحاسبية",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إضافة فترة جديدة",
            command=self.add_period_dialog,
            width=150
        ).pack(side="left", padx=5)

        # جدول الفترات
        self.create_periods_table()
        self.load_periods_data()

    def create_periods_table(self):
        """إنشاء جدول الفترات المحاسبية"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("رقم الفترة", "السنة المالية", "الفترة الحالية", "عدد الأشهر", "من شهر", "إلى شهر")
        self.periods_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.periods_tree.heading(col, text=col)
            self.periods_tree.column(col, width=120, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.periods_tree.yview)
        self.periods_tree.configure(yscrollcommand=scrollbar.set)

        self.periods_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_periods_data(self):
        """تحميل بيانات الفترات المحاسبية"""
        try:
            for item in self.periods_tree.get_children():
                self.periods_tree.delete(item)

            periods = self.db_manager.fetch_all("""
                SELECT period_number, fiscal_year, is_current, months_count, start_month, end_month
                FROM accounting_periods
                ORDER BY fiscal_year DESC
            """)

            for period in periods:
                current_status = "نعم" if period['is_current'] else "لا"

                self.periods_tree.insert("", "end", values=(
                    period['period_number'],
                    period['fiscal_year'],
                    current_status,
                    period['months_count'],
                    period['start_month'],
                    period['end_month']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الفترات المحاسبية:\n{str(e)}")

    def add_period_dialog(self):
        """حوار إضافة فترة محاسبية جديدة"""
        dialog = ctk.CTkToplevel()
        dialog.title("إضافة فترة محاسبية جديدة")
        dialog.geometry("500x600")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"500x600+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text="إضافة فترة محاسبية جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(dialog, width=450, height=450)
        scroll_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # حقول الإدخال
        fields = {}

        # رقم الفترة
        ctk.CTkLabel(scroll_frame, text="رقم الفترة:").pack(anchor="w", pady=5)
        fields['period_number'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['period_number'].pack(pady=5)

        # السنة المالية
        ctk.CTkLabel(scroll_frame, text="السنة المالية:").pack(anchor="w", pady=5)
        fields['fiscal_year'] = ctk.CTkEntry(scroll_frame, width=400)
        fields['fiscal_year'].pack(pady=5)
        fields['fiscal_year'].insert(0, str(datetime.now().year))

        # الفترة الحالية
        fields['is_current'] = ctk.CTkCheckBox(scroll_frame, text="الفترة الحالية")
        fields['is_current'].pack(anchor="w", pady=10)

        # عدد الأشهر
        ctk.CTkLabel(scroll_frame, text="عدد الأشهر:").pack(anchor="w", pady=5)
        fields['months_count'] = ctk.CTkComboBox(
            scroll_frame,
            values=[str(i) for i in range(1, 13)],
            width=400
        )
        fields['months_count'].pack(pady=5)
        fields['months_count'].set("12")

        # من شهر
        ctk.CTkLabel(scroll_frame, text="من شهر:").pack(anchor="w", pady=5)
        months = [
            "1 - يناير", "2 - فبراير", "3 - مارس", "4 - أبريل",
            "5 - مايو", "6 - يونيو", "7 - يوليو", "8 - أغسطس",
            "9 - سبتمبر", "10 - أكتوبر", "11 - نوفمبر", "12 - ديسمبر"
        ]
        fields['start_month'] = ctk.CTkComboBox(scroll_frame, values=months, width=400)
        fields['start_month'].pack(pady=5)
        fields['start_month'].set("1 - يناير")

        # إلى شهر
        ctk.CTkLabel(scroll_frame, text="إلى شهر:").pack(anchor="w", pady=5)
        fields['end_month'] = ctk.CTkComboBox(scroll_frame, values=months, width=400)
        fields['end_month'].pack(pady=5)
        fields['end_month'].set("12 - ديسمبر")

        # الملاحظات
        ctk.CTkLabel(scroll_frame, text="الملاحظات:").pack(anchor="w", pady=5)
        fields['notes'] = ctk.CTkTextbox(scroll_frame, width=400, height=80)
        fields['notes'].pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def save_period():
            """حفظ الفترة المحاسبية الجديدة"""
            period_number = fields['period_number'].get().strip()
            fiscal_year = fields['fiscal_year'].get().strip()
            is_current = fields['is_current'].get()
            months_count = int(fields['months_count'].get())
            start_month = int(fields['start_month'].get().split(" - ")[0])
            end_month = int(fields['end_month'].get().split(" - ")[0])
            notes = fields['notes'].get("1.0", "end-1c").strip()

            if not period_number or not fiscal_year:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            try:
                # إذا كانت الفترة الحالية، قم بإلغاء تحديد الفترات الأخرى
                if is_current:
                    self.db_manager.execute_query("""
                        UPDATE accounting_periods SET is_current = 0
                    """)

                self.db_manager.execute_query("""
                    INSERT INTO accounting_periods
                    (period_number, fiscal_year, is_current, months_count, start_month, end_month, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (period_number, int(fiscal_year), is_current, months_count, start_month, end_month, notes))

                messagebox.showinfo("نجح", "تم إضافة الفترة المحاسبية بنجاح")
                dialog.destroy()
                self.load_periods_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الفترة المحاسبية:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_period,
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def show_database_management(self):
        """عرض إدارة قواعد البيانات"""
        self.current_view = "database_management"
        self.clear_main_frame()
        self.highlight_button(self.database_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="إدارة قواعد البيانات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # قسم إعدادات النسخ الاحتياطي
        backup_settings_frame = ctk.CTkFrame(self.main_frame)
        backup_settings_frame.pack(fill="x", pady=10, padx=20)

        ctk.CTkLabel(
            backup_settings_frame,
            text="إعدادات النسخ الاحتياطي",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        backup_buttons_frame = ctk.CTkFrame(backup_settings_frame)
        backup_buttons_frame.pack(pady=10)

        ctk.CTkButton(
            backup_buttons_frame,
            text="إعدادات النسخ التلقائي",
            command=self.configure_auto_backup,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            backup_buttons_frame,
            text="جدولة النسخ الاحتياطي",
            command=self.schedule_backup,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        # قسم إعدادات الاتصال بالسيرفر
        server_settings_frame = ctk.CTkFrame(self.main_frame)
        server_settings_frame.pack(fill="x", pady=10, padx=20)

        ctk.CTkLabel(
            server_settings_frame,
            text="إعدادات الاتصال بالسيرفر",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        server_buttons_frame = ctk.CTkFrame(server_settings_frame)
        server_buttons_frame.pack(pady=10)

        ctk.CTkButton(
            server_buttons_frame,
            text="إعدادات الاتصال",
            command=self.configure_server_connection,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            server_buttons_frame,
            text="اختبار الاتصال",
            command=self.test_server_connection,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        # قسم إقفال قواعد البيانات
        closure_frame = ctk.CTkFrame(self.main_frame)
        closure_frame.pack(fill="x", pady=10, padx=20)

        ctk.CTkLabel(
            closure_frame,
            text="إقفال قواعد البيانات",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        closure_buttons_frame = ctk.CTkFrame(closure_frame)
        closure_buttons_frame.pack(pady=10)

        ctk.CTkButton(
            closure_buttons_frame,
            text="إقفال الحسابات",
            command=self.close_accounts,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            closure_buttons_frame,
            text="نقل أرصدة الحسابات",
            command=self.transfer_account_balances,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        # قسم إنشاء قاعدة بيانات جديدة
        new_db_frame = ctk.CTkFrame(self.main_frame)
        new_db_frame.pack(fill="x", pady=10, padx=20)

        ctk.CTkLabel(
            new_db_frame,
            text="إنشاء قاعدة بيانات جديدة",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        new_db_buttons_frame = ctk.CTkFrame(new_db_frame)
        new_db_buttons_frame.pack(pady=10)

        ctk.CTkButton(
            new_db_buttons_frame,
            text="إنشاء قاعدة بيانات جديدة",
            command=self.create_new_database,
            width=200,
            height=40
        ).pack(padx=10)

    def configure_auto_backup(self):
        """إعدادات النسخ التلقائي"""
        dialog = ctk.CTkToplevel()
        dialog.title("إعدادات النسخ التلقائي")
        dialog.geometry("400x300")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")

        ctk.CTkLabel(
            dialog,
            text="إعدادات النسخ التلقائي",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # تفعيل النسخ التلقائي
        auto_backup_var = ctk.CTkCheckBox(dialog, text="تفعيل النسخ التلقائي")
        auto_backup_var.pack(pady=10)

        # تكرار النسخ
        ctk.CTkLabel(dialog, text="تكرار النسخ:").pack(pady=5)
        frequency_combo = ctk.CTkComboBox(
            dialog,
            values=["يومي", "أسبوعي", "شهري"],
            width=200
        )
        frequency_combo.pack(pady=5)
        frequency_combo.set("يومي")

        # مسار الحفظ
        ctk.CTkLabel(dialog, text="مسار الحفظ:").pack(pady=5)
        path_entry = ctk.CTkEntry(dialog, width=300)
        path_entry.pack(pady=5)

        def browse_path():
            path = filedialog.askdirectory(title="اختر مجلد الحفظ")
            if path:
                path_entry.delete(0, "end")
                path_entry.insert(0, path)

        ctk.CTkButton(dialog, text="تصفح", command=browse_path).pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=lambda: [messagebox.showinfo("نجح", "تم حفظ الإعدادات"), dialog.destroy()],
            width=100
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def schedule_backup(self):
        """جدولة النسخ الاحتياطي"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def configure_server_connection(self):
        """إعدادات الاتصال بالسيرفر"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def test_server_connection(self):
        """اختبار الاتصال بالسيرفر"""
        messagebox.showinfo("اختبار الاتصال", "تم اختبار الاتصال بنجاح")

    def close_accounts(self):
        """إقفال الحسابات"""
        result = messagebox.askyesno(
            "تأكيد الإقفال",
            "هل أنت متأكد من إقفال الحسابات؟\nهذه العملية لا يمكن التراجع عنها."
        )
        if result:
            messagebox.showinfo("نجح", "تم إقفال الحسابات بنجاح")

    def transfer_account_balances(self):
        """نقل أرصدة الحسابات"""
        result = messagebox.askyesno(
            "تأكيد النقل",
            "هل أنت متأكد من نقل أرصدة الحسابات؟"
        )
        if result:
            messagebox.showinfo("نجح", "تم نقل أرصدة الحسابات بنجاح")

    def create_new_database(self):
        """إنشاء قاعدة بيانات جديدة"""
        result = messagebox.askyesno(
            "تأكيد الإنشاء",
            "هل أنت متأكد من إنشاء قاعدة بيانات جديدة؟\nسيتم فقدان البيانات الحالية."
        )
        if result:
            try:
                # إنشاء قاعدة بيانات جديدة
                new_db_path = filedialog.asksaveasfilename(
                    title="حفظ قاعدة البيانات الجديدة",
                    defaultextension=".db",
                    filetypes=[("Database files", "*.db")]
                )

                if new_db_path:
                    # إنشاء مدير قاعدة بيانات جديد
                    from src.database.database_manager import DatabaseManager
                    new_db = DatabaseManager(new_db_path)
                    new_db.initialize_database()

                    messagebox.showinfo("نجح", f"تم إنشاء قاعدة البيانات الجديدة:\n{new_db_path}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء قاعدة البيانات:\n{str(e)}")

    def show_database_closure(self):
        """عرض إقفال قواعد البيانات"""
        self.current_view = "database_closure"
        self.clear_main_frame()
        self.highlight_button(self.closure_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="إقفال قواعد البيانات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # قسم إقفال الحسابات
        closure_frame = ctk.CTkFrame(self.main_frame)
        closure_frame.pack(fill="x", pady=20, padx=20)

        ctk.CTkLabel(
            closure_frame,
            text="إقفال الحسابات",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        closure_info = """
        إقفال الحسابات يتضمن:
        • إقفال جميع الحسابات المؤقتة
        • ترحيل الأرصدة إلى الفترة التالية
        • إنشاء تقارير الإقفال
        • حفظ نسخة احتياطية قبل الإقفال
        """

        ctk.CTkLabel(
            closure_frame,
            text=closure_info,
            font=ctk.CTkFont(size=12),
            justify="right"
        ).pack(pady=10)

        closure_buttons_frame = ctk.CTkFrame(closure_frame)
        closure_buttons_frame.pack(pady=10)

        ctk.CTkButton(
            closure_buttons_frame,
            text="إقفال الحسابات",
            command=self.close_accounts_detailed,
            width=150,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            closure_buttons_frame,
            text="عرض الحسابات المقفلة",
            command=self.view_closed_accounts,
            width=150,
            height=40
        ).pack(side="left", padx=10)

        # قسم نقل أرصدة الحسابات
        transfer_frame = ctk.CTkFrame(self.main_frame)
        transfer_frame.pack(fill="x", pady=20, padx=20)

        ctk.CTkLabel(
            transfer_frame,
            text="نقل أرصدة الحسابات",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        transfer_info = """
        نقل أرصدة الحسابات يتضمن:
        • نقل الأرصدة من فترة محاسبية إلى أخرى
        • تحديث الأرصدة الافتتاحية
        • إنشاء قيود الترحيل
        • التحقق من توازن الميزانية
        """

        ctk.CTkLabel(
            transfer_frame,
            text=transfer_info,
            font=ctk.CTkFont(size=12),
            justify="right"
        ).pack(pady=10)

        transfer_buttons_frame = ctk.CTkFrame(transfer_frame)
        transfer_buttons_frame.pack(pady=10)

        ctk.CTkButton(
            transfer_buttons_frame,
            text="نقل أرصدة الحسابات",
            command=self.transfer_account_balances_detailed,
            width=150,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            transfer_buttons_frame,
            text="عرض عمليات النقل",
            command=self.view_transfer_operations,
            width=150,
            height=40
        ).pack(side="left", padx=10)

    def show_new_database(self):
        """عرض إنشاء قاعدة بيانات جديدة"""
        self.current_view = "new_database"
        self.clear_main_frame()
        self.highlight_button(self.new_db_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="إنشاء قاعدة بيانات جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # معلومات إنشاء قاعدة بيانات جديدة
        info_frame = ctk.CTkFrame(self.main_frame)
        info_frame.pack(fill="both", expand=True, pady=20, padx=20)

        info_text = """
        إنشاء قاعدة بيانات جديدة

        هذه العملية ستقوم بـ:
        • إنشاء قاعدة بيانات جديدة فارغة
        • إنشاء جميع الجداول المطلوبة
        • إدراج البيانات الافتراضية
        • إعداد الهيكل الأساسي للنظام

        تحذير: هذه العملية لا تؤثر على قاعدة البيانات الحالية

        خطوات الإنشاء:
        1. اختيار مكان حفظ قاعدة البيانات الجديدة
        2. تحديد اسم قاعدة البيانات
        3. إنشاء الهيكل الأساسي
        4. إدراج البيانات الافتراضية
        """

        ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=14),
            justify="right"
        ).pack(pady=30)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(info_frame)
        buttons_frame.pack(pady=20)

        ctk.CTkButton(
            buttons_frame,
            text="إنشاء قاعدة بيانات جديدة",
            command=self.create_new_database_detailed,
            width=200,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            buttons_frame,
            text="استيراد قاعدة بيانات",
            command=self.import_database,
            width=150,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            buttons_frame,
            text="تصدير قاعدة البيانات الحالية",
            command=self.export_current_database,
            width=180,
            height=40
        ).pack(side="left", padx=10)

    def close_accounts_detailed(self):
        """إقفال الحسابات مع التفاصيل"""
        dialog = ctk.CTkToplevel()
        dialog.title("إقفال الحسابات")
        dialog.geometry("500x400")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"500x400+{x}+{y}")

        ctk.CTkLabel(
            dialog,
            text="إقفال الحسابات",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # اختيار الفترة المحاسبية
        period_frame = ctk.CTkFrame(dialog)
        period_frame.pack(pady=10, padx=20, fill="x")

        ctk.CTkLabel(period_frame, text="اختر الفترة المحاسبية للإقفال:").pack(pady=5)
        period_combo = ctk.CTkComboBox(
            period_frame,
            values=["2024", "2023", "2022"],
            width=200
        )
        period_combo.pack(pady=5)

        # خيارات الإقفال
        options_frame = ctk.CTkFrame(dialog)
        options_frame.pack(pady=10, padx=20, fill="x")

        backup_var = ctk.CTkCheckBox(options_frame, text="إنشاء نسخة احتياطية قبل الإقفال")
        backup_var.pack(anchor="w", pady=5)
        backup_var.select()

        reports_var = ctk.CTkCheckBox(options_frame, text="إنشاء تقارير الإقفال")
        reports_var.pack(anchor="w", pady=5)
        reports_var.select()

        transfer_var = ctk.CTkCheckBox(options_frame, text="ترحيل الأرصدة للفترة التالية")
        transfer_var.pack(anchor="w", pady=5)
        transfer_var.select()

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def execute_closure():
            period = period_combo.get()
            if not period:
                messagebox.showerror("خطأ", "يرجى اختيار الفترة المحاسبية")
                return

            result = messagebox.askyesno(
                "تأكيد الإقفال",
                f"هل أنت متأكد من إقفال الحسابات للفترة {period}؟\nهذه العملية لا يمكن التراجع عنها."
            )

            if result:
                messagebox.showinfo("نجح", f"تم إقفال الحسابات للفترة {period} بنجاح")
                dialog.destroy()

        ctk.CTkButton(
            buttons_frame,
            text="تنفيذ الإقفال",
            command=execute_closure,
            width=120
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def view_closed_accounts(self):
        """عرض الحسابات المقفلة"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def transfer_account_balances_detailed(self):
        """نقل أرصدة الحسابات مع التفاصيل"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def view_transfer_operations(self):
        """عرض عمليات النقل"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def create_new_database_detailed(self):
        """إنشاء قاعدة بيانات جديدة مع التفاصيل"""
        dialog = ctk.CTkToplevel()
        dialog.title("إنشاء قاعدة بيانات جديدة")
        dialog.geometry("600x500")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"600x500+{x}+{y}")

        ctk.CTkLabel(
            dialog,
            text="إنشاء قاعدة بيانات جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # معلومات قاعدة البيانات
        info_frame = ctk.CTkFrame(dialog)
        info_frame.pack(pady=10, padx=20, fill="x")

        # اسم قاعدة البيانات
        ctk.CTkLabel(info_frame, text="اسم قاعدة البيانات:").pack(anchor="w", pady=5)
        db_name_entry = ctk.CTkEntry(info_frame, width=400)
        db_name_entry.pack(pady=5)
        db_name_entry.insert(0, f"payroll_db_{datetime.now().strftime('%Y%m%d')}")

        # مسار الحفظ
        ctk.CTkLabel(info_frame, text="مسار الحفظ:").pack(anchor="w", pady=5)
        path_frame = ctk.CTkFrame(info_frame)
        path_frame.pack(fill="x", pady=5)

        path_entry = ctk.CTkEntry(path_frame, width=300)
        path_entry.pack(side="left", padx=5)

        def browse_path():
            path = filedialog.askdirectory(title="اختر مجلد الحفظ")
            if path:
                path_entry.delete(0, "end")
                path_entry.insert(0, path)

        ctk.CTkButton(path_frame, text="تصفح", command=browse_path, width=80).pack(side="right", padx=5)

        # خيارات الإنشاء
        options_frame = ctk.CTkFrame(dialog)
        options_frame.pack(pady=10, padx=20, fill="x")

        ctk.CTkLabel(options_frame, text="خيارات الإنشاء:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", pady=5)

        create_tables_var = ctk.CTkCheckBox(options_frame, text="إنشاء جميع الجداول")
        create_tables_var.pack(anchor="w", pady=2)
        create_tables_var.select()

        insert_defaults_var = ctk.CTkCheckBox(options_frame, text="إدراج البيانات الافتراضية")
        insert_defaults_var.pack(anchor="w", pady=2)
        insert_defaults_var.select()

        create_admin_var = ctk.CTkCheckBox(options_frame, text="إنشاء حساب مدير افتراضي")
        create_admin_var.pack(anchor="w", pady=2)
        create_admin_var.select()

        # معلومات إضافية
        notes_frame = ctk.CTkFrame(dialog)
        notes_frame.pack(pady=10, padx=20, fill="both", expand=True)

        ctk.CTkLabel(notes_frame, text="ملاحظات:").pack(anchor="w", pady=5)
        notes_text = ctk.CTkTextbox(notes_frame, height=100)
        notes_text.pack(fill="both", expand=True, pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        def create_database():
            db_name = db_name_entry.get().strip()
            save_path = path_entry.get().strip()

            if not db_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم قاعدة البيانات")
                return

            if not save_path:
                messagebox.showerror("خطأ", "يرجى اختيار مسار الحفظ")
                return

            try:
                # إنشاء مسار قاعدة البيانات الكامل
                full_path = os.path.join(save_path, f"{db_name}.db")

                # إنشاء قاعدة البيانات
                from src.database.database_manager import DatabaseManager
                new_db = DatabaseManager(full_path)
                new_db.initialize_database()

                messagebox.showinfo("نجح", f"تم إنشاء قاعدة البيانات بنجاح:\n{full_path}")
                dialog.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء قاعدة البيانات:\n{str(e)}")

        ctk.CTkButton(
            buttons_frame,
            text="إنشاء قاعدة البيانات",
            command=create_database,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)

    def import_database(self):
        """استيراد قاعدة بيانات"""
        file_path = filedialog.askopenfilename(
            title="اختر قاعدة البيانات للاستيراد",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if file_path:
            result = messagebox.askyesno(
                "تأكيد الاستيراد",
                f"هل تريد استيراد قاعدة البيانات من:\n{file_path}"
            )

            if result:
                messagebox.showinfo("نجح", "تم استيراد قاعدة البيانات بنجاح")

    def export_current_database(self):
        """تصدير قاعدة البيانات الحالية"""
        save_path = filedialog.asksaveasfilename(
            title="حفظ نسخة من قاعدة البيانات",
            defaultextension=".db",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if save_path:
            try:
                success = self.db_manager.backup_database(save_path)
                if success:
                    messagebox.showinfo("نجح", f"تم تصدير قاعدة البيانات بنجاح:\n{save_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير قاعدة البيانات")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير قاعدة البيانات:\n{str(e)}")

    def show_backup_management(self):
        """عرض إدارة النسخ الاحتياطي"""
        self.current_view = "backup_management"
        self.clear_main_frame()
        self.highlight_button(self.backup_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="النسخ الاحتياطي",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=20, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إنشاء نسخة احتياطية",
            command=self.create_backup,
            width=150,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            buttons_frame,
            text="استرجاع نسخة احتياطية",
            command=self.restore_backup,
            width=150,
            height=40
        ).pack(side="left", padx=10)

        # معلومات النسخ الاحتياطي
        info_frame = ctk.CTkFrame(self.main_frame)
        info_frame.pack(pady=20, padx=20, fill="both", expand=True)

        info_text = """
        النسخ الاحتياطي لقاعدة البيانات

        • إنشاء نسخة احتياطية: حفظ نسخة من قاعدة البيانات الحالية
        • استرجاع نسخة احتياطية: استرجاع قاعدة البيانات من نسخة محفوظة

        تنبيه: يُنصح بإنشاء نسخة احتياطية بانتظام لحماية البيانات
        """

        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=14),
            justify="right"
        )
        info_label.pack(pady=50)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        backup_path = filedialog.asksaveasfilename(
            title="حفظ النسخة الاحتياطية",
            defaultextension=".db",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if backup_path:
            try:
                success = self.db_manager.backup_database(backup_path)
                if success:
                    messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_backup(self):
        """استرجاع نسخة احتياطية"""
        backup_path = filedialog.askopenfilename(
            title="اختر النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if backup_path:
            # تأكيد الاستعادة
            result = messagebox.askyesno(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استرجاع النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية."
            )

            if result:
                try:
                    success = self.db_manager.restore_database(backup_path)
                    if success:
                        messagebox.showinfo("نجح", "تم استرجاع النسخة الاحتياطية بنجاح")
                    else:
                        messagebox.showerror("خطأ", "فشل في استرجاع النسخة الاحتياطية")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في استرجاع النسخة الاحتياطية:\n{str(e)}")

    def highlight_button(self, active_button):
        """تمييز الزر النشط"""
        buttons = [
            self.institutions_btn, self.departments_btn, self.currencies_btn,
            self.accounts_btn, self.periods_btn, self.database_btn, self.backup_btn,
            self.closure_btn, self.new_db_btn
        ]

        for btn in buttons:
            if btn == active_button:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))

    def clear_main_frame(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()