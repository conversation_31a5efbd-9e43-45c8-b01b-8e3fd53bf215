# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import sqlite3
import os
from pathlib import Path
from datetime import datetime
import json

class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self, db_path=None):
        """تهيئة مدير قاعدة البيانات"""
        if db_path is None:
            # إنشاء مجلد البيانات
            data_dir = Path(__file__).parent.parent.parent / "data"
            data_dir.mkdir(exist_ok=True)
            self.db_path = data_dir / "payroll_system.db"
        else:
            self.db_path = Path(db_path)
        
        self.connection = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False
            )
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return cursor
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            raise e
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        cursor = self.execute_query(query, params)
        return cursor.fetchall()
    
    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        cursor = self.execute_query(query, params)
        return cursor.fetchone()
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        if not self.connect():
            raise Exception("فشل في الاتصال بقاعدة البيانات")
        
        # إنشاء الجداول
        self.create_tables()
        
        # إدراج البيانات الأساسية
        self.insert_default_data()
        
        print("تم تهيئة قاعدة البيانات بنجاح")
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المؤسسات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS institutions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                institution_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                address TEXT,
                email TEXT,
                website TEXT,
                notes TEXT,
                logo_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الدوائر
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS departments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                department_number TEXT UNIQUE NOT NULL,
                institution_id INTEGER,
                name TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (institution_id) REFERENCES institutions (id)
            )
        """)
        
        # جدول العملات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                currency_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                parts TEXT,
                notes TEXT,
                currency_type TEXT CHECK(currency_type IN ('محلية', 'أجنبية')) DEFAULT 'محلية',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الفترات المحاسبية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS accounting_periods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period_number TEXT UNIQUE NOT NULL,
                fiscal_year INTEGER NOT NULL,
                is_current BOOLEAN DEFAULT FALSE,
                months_count INTEGER DEFAULT 12,
                start_month INTEGER,
                end_month INTEGER,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول مجموعات المستخدمين
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS user_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                group_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول المستخدمين
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_number TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                account_type TEXT CHECK(account_type IN ('مدير', 'مستخدم')) DEFAULT 'مستخدم',
                department_id INTEGER,
                user_group_id INTEGER,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (department_id) REFERENCES departments (id),
                FOREIGN KEY (user_group_id) REFERENCES user_groups (id)
            )
        """)

        # جدول الأقسام
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول الشعب
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS divisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                division_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                section_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (section_id) REFERENCES sections (id)
            )
        """)

        # جدول العناوين الوظيفية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS job_titles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول الشهادات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS certificates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                certificate_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول الدرجات الوظيفية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS job_grades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                grade_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول المراحل
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS stages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stage_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول الموظفين
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_number TEXT UNIQUE NOT NULL,
                iban_number TEXT,
                full_name TEXT NOT NULL,
                gender TEXT CHECK(gender IN ('ذكر', 'أنثى')),
                marital_status TEXT CHECK(marital_status IN ('أعزب', 'متزوج', 'مطلق', 'أرمل')),
                job_title_id INTEGER,
                certificate_id INTEGER,
                job_grade_id INTEGER,
                stage_id INTEGER,
                birth_date DATE,
                hire_date DATE,
                status TEXT CHECK(status IN ('مستمر', 'متقاعد', 'متوفي', 'نقل', 'متوقف')) DEFAULT 'مستمر',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (job_title_id) REFERENCES job_titles (id),
                FOREIGN KEY (certificate_id) REFERENCES certificates (id),
                FOREIGN KEY (job_grade_id) REFERENCES job_grades (id),
                FOREIGN KEY (stage_id) REFERENCES stages (id)
            )
        """)

        # جدول دليل الحسابات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS chart_of_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_number TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                account_type TEXT,
                parent_account_id INTEGER,
                is_active BOOLEAN DEFAULT TRUE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts (id)
            )
        """)

        # جدول المصارف
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS banks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bank_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول فروع المصارف
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS bank_branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                branch_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                bank_id INTEGER,
                bank_account TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_id) REFERENCES banks (id)
            )
        """)

        # جدول الحسابات البنكية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_number TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                bank_branch_id INTEGER,
                balance DECIMAL(15,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_branch_id) REFERENCES bank_branches (id)
            )
        """)

        # جدول رواتب الموظفين
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS employee_salaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER,
                salary_month INTEGER,
                salary_year INTEGER,
                basic_salary DECIMAL(15,2) DEFAULT 0,
                government_contribution DECIMAL(15,2) DEFAULT 0,
                salary_difference DECIMAL(15,2) DEFAULT 0,

                -- المخصصات
                position_allowance DECIMAL(15,2) DEFAULT 0,
                marriage_allowance DECIMAL(15,2) DEFAULT 0,
                children_allowance DECIMAL(15,2) DEFAULT 0,
                engineering_allowance DECIMAL(15,2) DEFAULT 0,
                certificate_allowance DECIMAL(15,2) DEFAULT 0,
                craft_allowance DECIMAL(15,2) DEFAULT 0,
                danger_allowance DECIMAL(15,2) DEFAULT 0,
                location_allowance DECIMAL(15,2) DEFAULT 0,
                total_allowances DECIMAL(15,2) DEFAULT 0,

                -- الاستقطاعات
                retirement_fund DECIMAL(15,2) DEFAULT 0,
                department_contribution DECIMAL(15,2) DEFAULT 0,
                income_tax DECIMAL(15,2) DEFAULT 0,
                social_protection_fund DECIMAL(15,2) DEFAULT 0,
                health_insurance DECIMAL(15,2) DEFAULT 0,
                other_departments DECIMAL(15,2) DEFAULT 0,
                salary_difference_deduction DECIMAL(15,2) DEFAULT 0,
                execution_departments DECIMAL(15,2) DEFAULT 0,
                bank_installments DECIMAL(15,2) DEFAULT 0,
                total_deductions DECIMAL(15,2) DEFAULT 0,

                net_salary DECIMAL(15,2) DEFAULT 0,
                bank_account_id INTEGER,
                is_processed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id)
            )
        """)

        # جدول المعاملات المالية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS financial_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_number TEXT UNIQUE NOT NULL,
                transaction_type TEXT CHECK(transaction_type IN ('سند صرف', 'سند قبض', 'قيد يومية')),
                transaction_date DATE,
                description TEXT,
                amount DECIMAL(15,2),
                bank_account_id INTEGER,
                account_id INTEGER,
                reference_number TEXT,
                is_approved BOOLEAN DEFAULT FALSE,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id),
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)

        # جدول المصروفات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                expense_number TEXT UNIQUE NOT NULL,
                expense_date DATE,
                description TEXT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                expense_category TEXT,
                bank_account_id INTEGER,
                account_id INTEGER,
                is_approved BOOLEAN DEFAULT FALSE,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id),
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)

        print("تم إنشاء جداول النظام الأساسية")
    
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        
        # التحقق من وجود بيانات
        existing_currency = self.fetch_one("SELECT id FROM currencies LIMIT 1")
        if existing_currency:
            return  # البيانات موجودة مسبقاً
        
        # إدراج العملة الافتراضية
        self.execute_query("""
            INSERT INTO currencies (currency_number, name, symbol, parts, currency_type)
            VALUES (?, ?, ?, ?, ?)
        """, ("001", "الدينار العراقي", "IQD", "فلس", "محلية"))
        
        # إدراج الفترة المحاسبية الافتراضية
        current_year = datetime.now().year
        self.execute_query("""
            INSERT INTO accounting_periods 
            (period_number, fiscal_year, is_current, months_count, start_month, end_month)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (f"{current_year}", current_year, True, 12, 1, 12))
        
        print("تم إدراج البيانات الافتراضية")
    
    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def restore_database(self, backup_path):
        """استرجاع قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            self.disconnect()
            shutil.copy2(backup_path, self.db_path)
            self.connect()
            return True
        except Exception as e:
            print(f"خطأ في استرجاع النسخة الاحتياطية: {e}")
            return False
