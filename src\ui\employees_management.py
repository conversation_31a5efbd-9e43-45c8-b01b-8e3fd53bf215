# -*- coding: utf-8 -*-
"""
واجهة شؤون الموظفين
Employees Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime
from tkcalendar import DateEntry

class EmployeesManagement:
    """فئة إدارة شؤون الموظفين"""
    
    def __init__(self, parent_frame, db_manager):
        """تهيئة واجهة شؤون الموظفين"""
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.main_frame = None
        self.current_view = "directories"
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة شؤون الموظفين"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.parent_frame,
            text="شؤون الموظفين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # أزرار التبديل
        self.create_tab_buttons()
        
        # المنطقة الرئيسية
        self.main_frame = ctk.CTkFrame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # عرض الأدلة افتراضياً
        self.show_directories()
    
    def create_tab_buttons(self):
        """إنشاء أزرار التبديل بين الأقسام"""
        tab_frame = ctk.CTkFrame(self.parent_frame)
        tab_frame.pack(pady=10)
        
        # الصف الأول من الأزرار
        row1_frame = ctk.CTkFrame(tab_frame)
        row1_frame.pack(pady=5)
        
        self.directories_btn = ctk.CTkButton(
            row1_frame,
            text="الأدلة",
            command=self.show_directories,
            width=120
        )
        self.directories_btn.pack(side="left", padx=3)
        
        self.employees_btn = ctk.CTkButton(
            row1_frame,
            text="بيانات الموظفين",
            command=self.show_employees,
            width=120
        )
        self.employees_btn.pack(side="left", padx=3)
        
        self.reports_btn = ctk.CTkButton(
            row1_frame,
            text="تقارير الموظفين",
            command=self.show_employee_reports,
            width=120
        )
        self.reports_btn.pack(side="left", padx=3)
    
    def show_directories(self):
        """عرض الأدلة"""
        self.current_view = "directories"
        self.clear_main_frame()
        self.highlight_button(self.directories_btn)
        
        # عنوان القسم
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="الأدلة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        # إنشاء أزرار الأدلة
        self.create_directories_buttons()
        
        # المنطقة الفرعية
        self.sub_frame = ctk.CTkFrame(self.main_frame)
        self.sub_frame.pack(fill="both", expand=True, pady=10)
        
        # عرض دليل الأقسام افتراضياً
        self.show_sections_directory()
    
    def create_directories_buttons(self):
        """إنشاء أزرار الأدلة"""
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        # الصف الأول
        row1_frame = ctk.CTkFrame(buttons_frame)
        row1_frame.pack(pady=5)
        
        self.sections_btn = ctk.CTkButton(
            row1_frame,
            text="دليل الأقسام",
            command=self.show_sections_directory,
            width=120
        )
        self.sections_btn.pack(side="left", padx=3)
        
        self.divisions_btn = ctk.CTkButton(
            row1_frame,
            text="دليل الشعب",
            command=self.show_divisions_directory,
            width=120
        )
        self.divisions_btn.pack(side="left", padx=3)
        
        self.job_titles_btn = ctk.CTkButton(
            row1_frame,
            text="العناوين الوظيفية",
            command=self.show_job_titles_directory,
            width=120
        )
        self.job_titles_btn.pack(side="left", padx=3)
        
        # الصف الثاني
        row2_frame = ctk.CTkFrame(buttons_frame)
        row2_frame.pack(pady=5)
        
        self.certificates_btn = ctk.CTkButton(
            row2_frame,
            text="دليل الشهادات",
            command=self.show_certificates_directory,
            width=120
        )
        self.certificates_btn.pack(side="left", padx=3)
        
        self.job_grades_btn = ctk.CTkButton(
            row2_frame,
            text="الدرجات الوظيفية",
            command=self.show_job_grades_directory,
            width=120
        )
        self.job_grades_btn.pack(side="left", padx=3)
        
        self.stages_btn = ctk.CTkButton(
            row2_frame,
            text="دليل المراحل",
            command=self.show_stages_directory,
            width=120
        )
        self.stages_btn.pack(side="left", padx=3)
    
    def show_sections_directory(self):
        """عرض دليل الأقسام"""
        self.clear_sub_frame()
        self.highlight_directory_button(self.sections_btn)
        
        # عنوان الدليل
        title_label = ctk.CTkLabel(
            self.sub_frame,
            text="دليل الأقسام",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.sub_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        ctk.CTkButton(
            buttons_frame,
            text="إضافة قسم جديد",
            command=self.add_section_dialog,
            width=120
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="تعديل القسم",
            command=self.edit_section_dialog,
            width=120
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="حذف القسم",
            command=self.delete_section,
            width=120
        ).pack(side="left", padx=5)
        
        # جدول الأقسام
        self.create_sections_table()
        self.load_sections_data()
    
    def create_sections_table(self):
        """إنشاء جدول الأقسام"""
        table_frame = ctk.CTkFrame(self.sub_frame)
        table_frame.pack(fill="both", expand=True, pady=10)
        
        columns = ("رقم القسم", "اسم القسم", "تاريخ الإنشاء")
        self.sections_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.sections_tree.heading(col, text=col)
            self.sections_tree.column(col, width=150, anchor="center")
        
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.sections_tree.yview)
        self.sections_tree.configure(yscrollcommand=scrollbar.set)
        
        self.sections_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def load_sections_data(self):
        """تحميل بيانات الأقسام"""
        try:
            for item in self.sections_tree.get_children():
                self.sections_tree.delete(item)
            
            sections = self.db_manager.fetch_all("""
                SELECT section_number, name, created_at 
                FROM sections 
                ORDER BY section_number
            """)
            
            for section in sections:
                created_date = section['created_at'][:10] if section['created_at'] else ""
                
                self.sections_tree.insert("", "end", values=(
                    section['section_number'],
                    section['name'],
                    created_date
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الأقسام:\n{str(e)}")
    
    def add_section_dialog(self):
        """حوار إضافة قسم جديد"""
        dialog = ctk.CTkToplevel()
        dialog.title("إضافة قسم جديد")
        dialog.geometry("400x250")
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (250 // 2)
        dialog.geometry(f"400x250+{x}+{y}")
        
        # العنوان
        ctk.CTkLabel(
            dialog,
            text="إضافة قسم جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)
        
        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(pady=20, padx=20, fill="x")
        
        # رقم القسم
        ctk.CTkLabel(fields_frame, text="رقم القسم:").pack(anchor="w", pady=5)
        section_number_entry = ctk.CTkEntry(fields_frame, width=300)
        section_number_entry.pack(pady=5)
        
        # اسم القسم
        ctk.CTkLabel(fields_frame, text="اسم القسم:").pack(anchor="w", pady=5)
        section_name_entry = ctk.CTkEntry(fields_frame, width=300)
        section_name_entry.pack(pady=5)
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)
        
        def save_section():
            """حفظ القسم الجديد"""
            section_number = section_number_entry.get().strip()
            section_name = section_name_entry.get().strip()
            
            if not section_number or not section_name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            try:
                self.db_manager.execute_query("""
                    INSERT INTO sections (section_number, name)
                    VALUES (?, ?)
                """, (section_number, section_name))
                
                messagebox.showinfo("نجح", "تم إضافة القسم بنجاح")
                dialog.destroy()
                self.load_sections_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة القسم:\n{str(e)}")
        
        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_section,
            width=100
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)
    
    def edit_section_dialog(self):
        """حوار تعديل القسم"""
        selected = self.sections_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار قسم للتعديل")
            return
        
        # جلب بيانات القسم المحدد
        item = self.sections_tree.item(selected[0])
        values = item['values']
        
        dialog = ctk.CTkToplevel()
        dialog.title("تعديل القسم")
        dialog.geometry("400x250")
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (250 // 2)
        dialog.geometry(f"400x250+{x}+{y}")
        
        # العنوان
        ctk.CTkLabel(
            dialog,
            text="تعديل القسم",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)
        
        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(pady=20, padx=20, fill="x")
        
        # رقم القسم
        ctk.CTkLabel(fields_frame, text="رقم القسم:").pack(anchor="w", pady=5)
        section_number_entry = ctk.CTkEntry(fields_frame, width=300)
        section_number_entry.pack(pady=5)
        section_number_entry.insert(0, values[0])
        
        # اسم القسم
        ctk.CTkLabel(fields_frame, text="اسم القسم:").pack(anchor="w", pady=5)
        section_name_entry = ctk.CTkEntry(fields_frame, width=300)
        section_name_entry.pack(pady=5)
        section_name_entry.insert(0, values[1])
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)
        
        def update_section():
            """تحديث القسم"""
            new_section_number = section_number_entry.get().strip()
            new_section_name = section_name_entry.get().strip()
            
            if not new_section_number or not new_section_name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            try:
                self.db_manager.execute_query("""
                    UPDATE sections 
                    SET section_number = ?, name = ?
                    WHERE section_number = ?
                """, (new_section_number, new_section_name, values[0]))
                
                messagebox.showinfo("نجح", "تم تحديث القسم بنجاح")
                dialog.destroy()
                self.load_sections_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث القسم:\n{str(e)}")
        
        ctk.CTkButton(
            buttons_frame,
            text="تحديث",
            command=update_section,
            width=100
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)
    
    def delete_section(self):
        """حذف القسم"""
        selected = self.sections_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار قسم للحذف")
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا القسم؟")
        if not result:
            return
        
        # جلب رقم القسم
        item = self.sections_tree.item(selected[0])
        section_number = item['values'][0]
        
        try:
            self.db_manager.execute_query("""
                DELETE FROM sections WHERE section_number = ?
            """, (section_number,))
            
            messagebox.showinfo("نجح", "تم حذف القسم بنجاح")
            self.load_sections_data()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف القسم:\n{str(e)}")
    
    def show_divisions_directory(self):
        """عرض دليل الشعب"""
        self.clear_sub_frame()
        self.highlight_directory_button(self.divisions_btn)
        
        title_label = ctk.CTkLabel(
            self.sub_frame,
            text="دليل الشعب",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.sub_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=14)
        )
        placeholder_label.pack(pady=50)
    
    def show_job_titles_directory(self):
        """عرض دليل العناوين الوظيفية"""
        self.clear_sub_frame()
        self.highlight_directory_button(self.job_titles_btn)
        
        title_label = ctk.CTkLabel(
            self.sub_frame,
            text="العناوين الوظيفية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.sub_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=14)
        )
        placeholder_label.pack(pady=50)
    
    def show_certificates_directory(self):
        """عرض دليل الشهادات"""
        self.clear_sub_frame()
        self.highlight_directory_button(self.certificates_btn)
        
        title_label = ctk.CTkLabel(
            self.sub_frame,
            text="دليل الشهادات",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.sub_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=14)
        )
        placeholder_label.pack(pady=50)
    
    def show_job_grades_directory(self):
        """عرض دليل الدرجات الوظيفية"""
        self.clear_sub_frame()
        self.highlight_directory_button(self.job_grades_btn)
        
        title_label = ctk.CTkLabel(
            self.sub_frame,
            text="الدرجات الوظيفية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.sub_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=14)
        )
        placeholder_label.pack(pady=50)
    
    def show_stages_directory(self):
        """عرض دليل المراحل"""
        self.clear_sub_frame()
        self.highlight_directory_button(self.stages_btn)
        
        title_label = ctk.CTkLabel(
            self.sub_frame,
            text="دليل المراحل",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.sub_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=14)
        )
        placeholder_label.pack(pady=50)

    def show_employees(self):
        """عرض بيانات الموظفين"""
        self.current_view = "employees"
        self.clear_main_frame()
        self.highlight_button(self.employees_btn)

        # عنوان القسم
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="بيانات الموظفين",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            buttons_frame,
            text="إضافة موظف جديد",
            command=self.add_employee_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="تعديل الموظف",
            command=self.edit_employee_dialog,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="حذف الموظف",
            command=self.delete_employee,
            width=150
        ).pack(side="left", padx=5)

        # جدول الموظفين
        self.create_employees_table()
        self.load_employees_data()

    def create_employees_table(self):
        """إنشاء جدول الموظفين"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("الرقم الوظيفي", "رقم الآيبان", "الاسم الرباعي", "الجنس", "الحالة الزوجية", "العنوان الوظيفي", "الحالة")
        self.employees_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=120, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.employees_tree.yview)
        self.employees_tree.configure(yscrollcommand=scrollbar.set)

        self.employees_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_employees_data(self):
        """تحميل بيانات الموظفين"""
        try:
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)

            employees = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.iban_number, e.full_name, e.gender,
                       e.marital_status, jt.name as job_title, e.status
                FROM employees e
                LEFT JOIN job_titles jt ON e.job_title_id = jt.id
                ORDER BY e.employee_number
            """)

            for emp in employees:
                self.employees_tree.insert("", "end", values=(
                    emp['employee_number'],
                    emp['iban_number'] or "",
                    emp['full_name'],
                    emp['gender'] or "",
                    emp['marital_status'] or "",
                    emp['job_title'] or "",
                    emp['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الموظفين:\n{str(e)}")

    def add_employee_dialog(self):
        """حوار إضافة موظف جديد"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def edit_employee_dialog(self):
        """حوار تعديل الموظف"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def delete_employee(self):
        """حذف الموظف"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def show_employee_reports(self):
        """عرض تقارير الموظفين"""
        self.current_view = "employee_reports"
        self.clear_main_frame()
        self.highlight_button(self.reports_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="تقارير الموظفين",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(pady=50)

    def highlight_button(self, active_button):
        """تمييز الزر النشط"""
        buttons = [self.directories_btn, self.employees_btn, self.reports_btn]

        for btn in buttons:
            if btn == active_button:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))

    def highlight_directory_button(self, active_button):
        """تمييز زر الدليل النشط"""
        buttons = [
            self.sections_btn, self.divisions_btn, self.job_titles_btn,
            self.certificates_btn, self.job_grades_btn, self.stages_btn
        ]

        for btn in buttons:
            if btn == active_button:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))

    def clear_main_frame(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()

    def clear_sub_frame(self):
        """مسح المحتوى الفرعي"""
        if hasattr(self, 'sub_frame'):
            for widget in self.sub_frame.winfo_children():
                widget.destroy()
