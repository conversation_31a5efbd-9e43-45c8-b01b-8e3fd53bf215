# -*- coding: utf-8 -*-
"""
نظام السجلات
Logging System
"""

import logging
import os
from datetime import datetime
from pathlib import Path

class Logger:
    """فئة إدارة السجلات"""
    
    def __init__(self, name="PayrollSystem"):
        """تهيئة نظام السجلات"""
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # إنشاء مجلد السجلات
        log_dir = Path(__file__).parent.parent.parent / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # ملف السجل
        log_file = log_dir / f"payroll_{datetime.now().strftime('%Y%m%d')}.log"
        
        # إعداد معالج الملف
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # إعداد معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # تنسيق الرسائل
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # إضافة المعالجات
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def debug(self, message):
        """رسالة تصحيح"""
        self.logger.debug(message)
    
    def info(self, message):
        """رسالة معلومات"""
        self.logger.info(message)
    
    def warning(self, message):
        """رسالة تحذير"""
        self.logger.warning(message)
    
    def error(self, message):
        """رسالة خطأ"""
        self.logger.error(message)
    
    def critical(self, message):
        """رسالة خطأ فادح"""
        self.logger.critical(message)
