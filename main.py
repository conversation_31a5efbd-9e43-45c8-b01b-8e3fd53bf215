#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الرواتب اللامركزي
Decentralized Payroll Management System

المطور: Augment Agent
التاريخ: 2025-06-25
الإصدار: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk
from pathlib import Path

# إضافة مجلد المشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات الأساسية
from src.database.database_manager import DatabaseManager
from src.ui.main_window import MainWindow
from src.utils.config import Config
from src.utils.logger import Logger

class PayrollApp:
    """الفئة الرئيسية للتطبيق"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.config = Config()
        self.logger = Logger()
        self.db_manager = None
        self.main_window = None
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()
            self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تهيئة قاعدة البيانات:\n{str(e)}")
            return False
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow(self.db_manager, self.config)
            
            # تشغيل التطبيق
            self.main_window.run()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق:\n{str(e)}")

def main():
    """النقطة الرئيسية لتشغيل التطبيق"""
    try:
        # تعيين إعدادات CustomTkinter
        ctk.set_appearance_mode("light")  # أو "dark" أو "system"
        ctk.set_default_color_theme("blue")  # أو "green" أو "dark-blue"
        
        # إنشاء وتشغيل التطبيق
        app = PayrollApp()
        app.run()
        
    except Exception as e:
        messagebox.showerror("خطأ فادح", f"فشل في بدء التطبيق:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
