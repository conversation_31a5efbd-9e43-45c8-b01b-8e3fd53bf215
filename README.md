# نظام إدارة الرواتب اللامركزي
## Decentralized Payroll Management System

نظام محاسبي متكامل لإدارة رواتب الموظفين مصمم خصيصاً للمؤسسات العراقية

---

## 🌟 المميزات الرئيسية

### 👥 إدارة المستخدمين
- إنشاء وإدارة مجموعات المستخدمين
- إدارة حسابات المستخدمين مع صلاحيات مختلفة
- نظام تسجيل دخول آمن

### ⚙️ الإعدادات والتهيئة
- إدارة بيانات المؤسسة والدوائر
- دليل العملات المحلية والأجنبية
- إدارة الفترات المحاسبية
- النسخ الاحتياطي واستعادة قاعدة البيانات

### 🧑‍💼 شؤون الموظفين
- **الأدلة:**
  - دليل الأقسام والشعب
  - العناوين الوظيفية
  - دليل الشهادات
  - الدرجات الوظيفية والمراحل
- **بيانات الموظفين:**
  - الرقم الوظيفي ورقم الآيبان
  - البيانات الشخصية والوظيفية
  - تواريخ التعيين والميلاد
  - حالة الموظف (مستمر، متقاعد، إلخ)

### 💰 نظام الرواتب المتقدم
- **حساب الرواتب:**
  - الراتب الأساسي وحصة الدائرة
  - جميع أنواع المخصصات (المنصب، الزوجية، الأولاد، الهندسية، الشهادة، الحرفة، الخطورة، الموقع الجغرافي)
  - الاستقطاعات التلقائية (صندوق التقاعد 10%, حصة الدائرة 15%)
  - الاستقطاعات الأخرى (ضريبة الدخل، الحماية الاجتماعية، الضمان الصحي، أقساط المصارف)
- **مراجعة الرواتب:**
  - عرض الرواتب المحفوظة
  - إمكانية التعديل والمراجعة
- **تقارير الرواتب:**
  - تقرير الرواتب الشهري
  - تقارير المخصصات والاستقطاعات
  - تقرير صافي الرواتب
  - تقرير حسب الحساب البنكي

### 💳 إدارة المصروفات
- إضافة وتصنيف المصروفات
- ربط المصروفات بالحسابات البنكية والمحاسبية
- نظام اعتماد المصروفات
- تقارير المصروفات المختلفة

### 📊 نظام التقارير الشامل
- **التقارير المالية:**
  - تقرير اليومية العامة
  - كشف الحسابات
  - تقرير أرصدة الحسابات
  - تقرير سندات الصرف والقبض
  - تقرير الأرصدة الافتتاحية
  - تقرير القيود اليومية
- **تقارير الرواتب:**
  - جميع تقارير الرواتب المفصلة
- **تقارير المصروفات:**
  - تقارير شهرية وسنوية
  - تقارير حسب الفئة
- **تصدير التقارير:** إمكانية حفظ التقارير كملفات PDF

---

## 🛠️ التقنيات المستخدمة

- **لغة البرمجة:** Python 3.7+
- **واجهة المستخدم:** CustomTkinter (واجهة عصرية وجذابة)
- **قاعدة البيانات:** SQLite (قاعدة بيانات محلية آمنة)
- **التقارير:** ReportLab (إنتاج ملفات PDF احترافية)
- **التواريخ:** tkcalendar (اختيار التواريخ بسهولة)
- **الجداول:** pandas, openpyxl (معالجة البيانات)
- **الرسوم البيانية:** matplotlib (الإحصائيات المرئية)

---

## 📋 متطلبات النظام

- **نظام التشغيل:** Windows 7 أو أحدث
- **Python:** الإصدار 3.7 أو أحدث
- **الذاكرة:** 4 جيجابايت RAM كحد أدنى
- **مساحة القرص:** 500 ميجابايت للتطبيق + مساحة إضافية للبيانات

---

## 🚀 التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.7+ على نظامك

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python main.py
```

---

## 📁 هيكل المشروع

```
Emp/
├── main.py                 # نقطة البداية الرئيسية
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # دليل المستخدم
├── src/                   # الكود المصدري
│   ├── database/          # إدارة قاعدة البيانات
│   │   └── database_manager.py
│   ├── ui/                # واجهات المستخدم
│   │   ├── main_window.py
│   │   ├── users_management.py
│   │   ├── settings_management.py
│   │   ├── employees_management.py
│   │   ├── salaries_management.py
│   │   ├── expenses_management.py
│   │   └── reports_management.py
│   ├── models/            # نماذج البيانات
│   ├── utils/             # الأدوات المساعدة
│   │   ├── config.py
│   │   └── logger.py
│   └── reports/           # نظام التقارير
├── data/                  # قاعدة البيانات والملفات
├── assets/                # الموارد (الأيقونات والخطوط)
├── logs/                  # ملفات السجلات
└── reports/               # التقارير المُنتجة
```

---

## 🎯 الاستخدام

### البدء السريع
1. شغل التطبيق باستخدام `python main.py`
2. ستظهر الواجهة الرئيسية مع القائمة الجانبية على اليمين
3. ابدأ بإعداد بيانات المؤسسة من قسم "الإعدادات والتهيئة"
4. أضف الموظفين من قسم "شؤون الموظفين"
5. احسب الرواتب من قسم "الرواتب"

### نصائح مهمة
- **النسخ الاحتياطي:** قم بإنشاء نسخة احتياطية من قاعدة البيانات بانتظام
- **الخط المستخدم:** التطبيق مُحسَّن لخط Cairo 12pt Bold
- **اللغة:** الواجهة باللغة العربية مع دعم كامل للنصوص العربية

---

## 🔧 الميزات المتقدمة

### نظام الصلاحيات
- مدير النظام: صلاحيات كاملة
- المستخدم العادي: صلاحيات محدودة

### الأمان
- تشفير كلمات المرور
- سجلات العمليات
- نظام النسخ الاحتياطي التلقائي

### التخصيص
- إعدادات قابلة للتخصيص
- ألوان وثيمات متعددة
- دعم شعارات المؤسسات

---

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- **المطور:** Augment Agent
- **الإصدار:** 1.0.0
- **تاريخ الإصدار:** 2025-06-25

---

## 📄 الترخيص

هذا المشروع مطور خصيصاً للمؤسسات العراقية ويخضع لحقوق الطبع والنشر.

---

## 🙏 شكر وتقدير

تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء العالي والموثوقية في إدارة رواتب الموظفين.

**جميع الحقوق محفوظة © 2025**
