# -*- coding: utf-8 -*-
"""
واجهة إدارة التقارير
Reports Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from datetime import datetime
import calendar
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.units import inch
import os

class ReportsManagement:
    """فئة إدارة التقارير"""
    
    def __init__(self, parent_frame, db_manager):
        """تهيئة واجهة إدارة التقارير"""
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.main_frame = None
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة إدارة التقارير"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.parent_frame,
            text="التقارير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # المنطقة الرئيسية
        self.main_frame = ctk.CTkFrame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء أقسام التقارير
        self.create_reports_sections()
    
    def create_reports_sections(self):
        """إنشاء أقسام التقارير"""
        
        # قسم التقارير المالية
        financial_frame = ctk.CTkFrame(self.main_frame)
        financial_frame.pack(fill="x", pady=10, padx=10)
        
        ctk.CTkLabel(
            financial_frame,
            text="التقارير المالية",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)
        
        # أزرار التقارير المالية
        financial_buttons_frame = ctk.CTkFrame(financial_frame)
        financial_buttons_frame.pack(pady=10)
        
        # الصف الأول
        row1_frame = ctk.CTkFrame(financial_buttons_frame)
        row1_frame.pack(pady=5)
        
        ctk.CTkButton(
            row1_frame,
            text="تقرير اليومية العامة",
            command=self.generate_general_journal_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            row1_frame,
            text="كشف حساب",
            command=self.generate_account_statement,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            row1_frame,
            text="تقرير أرصدة الحسابات",
            command=self.generate_account_balances_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        # الصف الثاني
        row2_frame = ctk.CTkFrame(financial_buttons_frame)
        row2_frame.pack(pady=5)
        
        ctk.CTkButton(
            row2_frame,
            text="تقرير سندات الصرف والقبض",
            command=self.generate_vouchers_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            row2_frame,
            text="تقرير الأرصدة الافتتاحية",
            command=self.generate_opening_balances_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            row2_frame,
            text="تقرير القيود اليومية",
            command=self.generate_daily_entries_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        # قسم تقارير الرواتب
        salary_frame = ctk.CTkFrame(self.main_frame)
        salary_frame.pack(fill="x", pady=10, padx=10)
        
        ctk.CTkLabel(
            salary_frame,
            text="تقارير الرواتب",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)
        
        # أزرار تقارير الرواتب
        salary_buttons_frame = ctk.CTkFrame(salary_frame)
        salary_buttons_frame.pack(pady=10)
        
        # الصف الأول
        salary_row1_frame = ctk.CTkFrame(salary_buttons_frame)
        salary_row1_frame.pack(pady=5)
        
        ctk.CTkButton(
            salary_row1_frame,
            text="تقرير الرواتب الشهري",
            command=self.generate_monthly_salary_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            salary_row1_frame,
            text="تقرير المخصصات",
            command=self.generate_allowances_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            salary_row1_frame,
            text="تقرير الاستقطاعات",
            command=self.generate_deductions_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        # الصف الثاني
        salary_row2_frame = ctk.CTkFrame(salary_buttons_frame)
        salary_row2_frame.pack(pady=5)
        
        ctk.CTkButton(
            salary_row2_frame,
            text="تقرير صافي الرواتب",
            command=self.generate_net_salary_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            salary_row2_frame,
            text="تقرير حسب الحساب البنكي",
            command=self.generate_bank_account_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            salary_row2_frame,
            text="تقرير الموظفين",
            command=self.generate_employees_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        # قسم تقارير المصروفات
        expenses_frame = ctk.CTkFrame(self.main_frame)
        expenses_frame.pack(fill="x", pady=10, padx=10)
        
        ctk.CTkLabel(
            expenses_frame,
            text="تقارير المصروفات",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)
        
        # أزرار تقارير المصروفات
        expenses_buttons_frame = ctk.CTkFrame(expenses_frame)
        expenses_buttons_frame.pack(pady=10)
        
        ctk.CTkButton(
            expenses_buttons_frame,
            text="تقرير المصروفات الشهري",
            command=self.generate_monthly_expenses_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            expenses_buttons_frame,
            text="تقرير المصروفات حسب الفئة",
            command=self.generate_expenses_by_category_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            expenses_buttons_frame,
            text="تقرير المصروفات السنوي",
            command=self.generate_annual_expenses_report,
            width=180,
            height=40
        ).pack(side="left", padx=5)
    
    def get_report_period(self):
        """الحصول على فترة التقرير من المستخدم"""
        dialog = ctk.CTkToplevel()
        dialog.title("اختيار فترة التقرير")
        dialog.geometry("400x300")
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")
        
        # العنوان
        ctk.CTkLabel(
            dialog,
            text="اختيار فترة التقرير",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)
        
        # إطار الاختيار
        selection_frame = ctk.CTkFrame(dialog)
        selection_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        # اختيار الشهر
        ctk.CTkLabel(selection_frame, text="الشهر:").pack(pady=5)
        month_combo = ctk.CTkComboBox(
            selection_frame,
            values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
            width=300
        )
        month_combo.pack(pady=5)
        month_combo.set(f"{datetime.now().month:02d} - {calendar.month_name[datetime.now().month]}")
        
        # اختيار السنة
        ctk.CTkLabel(selection_frame, text="السنة:").pack(pady=5)
        current_year = datetime.now().year
        year_combo = ctk.CTkComboBox(
            selection_frame,
            values=[str(year) for year in range(current_year-5, current_year+5)],
            width=300
        )
        year_combo.pack(pady=5)
        year_combo.set(str(current_year))
        
        # متغير لحفظ النتيجة
        result = {"month": None, "year": None, "confirmed": False}
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)
        
        def confirm_selection():
            result["month"] = int(month_combo.get().split(" - ")[0])
            result["year"] = int(year_combo.get())
            result["confirmed"] = True
            dialog.destroy()
        
        ctk.CTkButton(
            buttons_frame,
            text="تأكيد",
            command=confirm_selection,
            width=100
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100
        ).pack(side="left", padx=5)
        
        # انتظار إغلاق النافذة
        dialog.wait_window()
        
        return result if result["confirmed"] else None
    
    def save_report_as_pdf(self, title, data, headers):
        """حفظ التقرير كملف PDF"""
        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ التقرير",
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
            )
            
            if not filename:
                return
            
            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=A4)
            story = []
            
            # الأنماط
            styles = getSampleStyleSheet()
            
            # العنوان
            title_style = styles['Title']
            title_para = Paragraph(title, title_style)
            story.append(title_para)
            story.append(Spacer(1, 12))
            
            # تاريخ التقرير
            date_para = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal'])
            story.append(date_para)
            story.append(Spacer(1, 12))
            
            # الجدول
            if data:
                # إضافة العناوين
                table_data = [headers] + data
                
                # إنشاء الجدول
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            else:
                no_data_para = Paragraph("لا توجد بيانات للعرض", styles['Normal'])
                story.append(no_data_para)
            
            # بناء المستند
            doc.build(story)
            
            messagebox.showinfo("نجح", f"تم حفظ التقرير بنجاح:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التقرير:\n{str(e)}")
    
    # التقارير المالية
    def generate_general_journal_report(self):
        """إنتاج تقرير اليومية العامة"""
        period = self.get_report_period()
        if not period:
            return
        
        try:
            # جلب البيانات من قاعدة البيانات
            transactions = self.db_manager.fetch_all("""
                SELECT transaction_number, transaction_date, description, amount, transaction_type
                FROM financial_transactions
                WHERE strftime('%m', transaction_date) = ? AND strftime('%Y', transaction_date) = ?
                ORDER BY transaction_date
            """, (f"{period['month']:02d}", str(period['year'])))
            
            # تحضير البيانات للتقرير
            headers = ["رقم المعاملة", "التاريخ", "الوصف", "المبلغ", "نوع المعاملة"]
            data = []
            
            for trans in transactions:
                data.append([
                    trans['transaction_number'],
                    trans['transaction_date'],
                    trans['description'],
                    f"{trans['amount']:.2f}",
                    trans['transaction_type']
                ])
            
            # حفظ التقرير
            title = f"تقرير اليومية العامة - {period['month']:02d}/{period['year']}"
            self.save_report_as_pdf(title, data, headers)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")
    
    def generate_account_statement(self):
        """إنتاج كشف حساب"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def generate_account_balances_report(self):
        """إنتاج تقرير أرصدة الحسابات"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def generate_vouchers_report(self):
        """إنتاج تقرير سندات الصرف والقبض"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def generate_opening_balances_report(self):
        """إنتاج تقرير الأرصدة الافتتاحية"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def generate_daily_entries_report(self):
        """إنتاج تقرير القيود اليومية"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    # تقارير الرواتب
    def generate_monthly_salary_report(self):
        """إنتاج تقرير الرواتب الشهري"""
        period = self.get_report_period()
        if not period:
            return

        try:
            # جلب البيانات من قاعدة البيانات
            salaries = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.full_name, s.basic_salary, s.total_allowances,
                       s.total_deductions, s.net_salary
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.salary_month = ? AND s.salary_year = ? AND s.is_processed = 1
                ORDER BY e.employee_number
            """, (period['month'], period['year']))

            # تحضير البيانات للتقرير
            headers = ["الرقم الوظيفي", "الاسم الرباعي", "الراتب الأساسي", "إجمالي المخصصات", "إجمالي الاستقطاعات", "صافي الراتب"]
            data = []
            total_basic = 0
            total_allowances = 0
            total_deductions = 0
            total_net = 0

            for salary in salaries:
                data.append([
                    salary['employee_number'],
                    salary['full_name'],
                    f"{salary['basic_salary']:.2f}",
                    f"{salary['total_allowances']:.2f}",
                    f"{salary['total_deductions']:.2f}",
                    f"{salary['net_salary']:.2f}"
                ])

                total_basic += salary['basic_salary']
                total_allowances += salary['total_allowances']
                total_deductions += salary['total_deductions']
                total_net += salary['net_salary']

            # إضافة صف الإجمالي
            data.append([
                "الإجمالي",
                "",
                f"{total_basic:.2f}",
                f"{total_allowances:.2f}",
                f"{total_deductions:.2f}",
                f"{total_net:.2f}"
            ])

            # حفظ التقرير
            title = f"تقرير الرواتب الشهري - {period['month']:02d}/{period['year']}"
            self.save_report_as_pdf(title, data, headers)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

    def generate_allowances_report(self):
        """إنتاج تقرير المخصصات"""
        period = self.get_report_period()
        if not period:
            return

        try:
            # جلب البيانات من قاعدة البيانات
            allowances = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.full_name, s.position_allowance, s.marriage_allowance,
                       s.children_allowance, s.engineering_allowance, s.certificate_allowance,
                       s.craft_allowance, s.danger_allowance, s.location_allowance, s.total_allowances
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.salary_month = ? AND s.salary_year = ? AND s.is_processed = 1
                ORDER BY e.employee_number
            """, (period['month'], period['year']))

            # تحضير البيانات للتقرير
            headers = [
                "الرقم الوظيفي", "الاسم الرباعي", "مخصصات المنصب", "مخصصات الزوجية",
                "مخصصات الأولاد", "مخصصات هندسية", "مخصصات الشهادة", "مخصصات الحرفة",
                "مخصصات الخطورة", "مخصصات الموقع", "إجمالي المخصصات"
            ]
            data = []

            for allowance in allowances:
                data.append([
                    allowance['employee_number'],
                    allowance['full_name'],
                    f"{allowance['position_allowance']:.2f}",
                    f"{allowance['marriage_allowance']:.2f}",
                    f"{allowance['children_allowance']:.2f}",
                    f"{allowance['engineering_allowance']:.2f}",
                    f"{allowance['certificate_allowance']:.2f}",
                    f"{allowance['craft_allowance']:.2f}",
                    f"{allowance['danger_allowance']:.2f}",
                    f"{allowance['location_allowance']:.2f}",
                    f"{allowance['total_allowances']:.2f}"
                ])

            # حفظ التقرير
            title = f"تقرير المخصصات - {period['month']:02d}/{period['year']}"
            self.save_report_as_pdf(title, data, headers)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

    def generate_deductions_report(self):
        """إنتاج تقرير الاستقطاعات"""
        period = self.get_report_period()
        if not period:
            return

        try:
            # جلب البيانات من قاعدة البيانات
            deductions = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.full_name, s.retirement_fund, s.department_contribution,
                       s.income_tax, s.social_protection_fund, s.health_insurance, s.other_departments,
                       s.salary_difference_deduction, s.execution_departments, s.bank_installments, s.total_deductions
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.salary_month = ? AND s.salary_year = ? AND s.is_processed = 1
                ORDER BY e.employee_number
            """, (period['month'], period['year']))

            # تحضير البيانات للتقرير
            headers = [
                "الرقم الوظيفي", "الاسم الرباعي", "صندوق التقاعد", "حصة الدائرة",
                "ضريبة الدخل", "الحماية الاجتماعية", "الضمان الصحي", "دوائر أخرى",
                "فرق راتب", "دوائر التنفيذ", "أقساط المصارف", "إجمالي الاستقطاعات"
            ]
            data = []

            for deduction in deductions:
                data.append([
                    deduction['employee_number'],
                    deduction['full_name'],
                    f"{deduction['retirement_fund']:.2f}",
                    f"{deduction['department_contribution']:.2f}",
                    f"{deduction['income_tax']:.2f}",
                    f"{deduction['social_protection_fund']:.2f}",
                    f"{deduction['health_insurance']:.2f}",
                    f"{deduction['other_departments']:.2f}",
                    f"{deduction['salary_difference_deduction']:.2f}",
                    f"{deduction['execution_departments']:.2f}",
                    f"{deduction['bank_installments']:.2f}",
                    f"{deduction['total_deductions']:.2f}"
                ])

            # حفظ التقرير
            title = f"تقرير الاستقطاعات - {period['month']:02d}/{period['year']}"
            self.save_report_as_pdf(title, data, headers)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

    def generate_net_salary_report(self):
        """إنتاج تقرير صافي الرواتب"""
        period = self.get_report_period()
        if not period:
            return

        try:
            # جلب البيانات من قاعدة البيانات
            salaries = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.iban_number, e.full_name, s.net_salary
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.salary_month = ? AND s.salary_year = ? AND s.is_processed = 1
                ORDER BY e.employee_number
            """, (period['month'], period['year']))

            # تحضير البيانات للتقرير
            headers = ["الرقم الوظيفي", "رقم الآيبان", "الاسم الرباعي", "صافي الراتب"]
            data = []
            total_net = 0

            for salary in salaries:
                data.append([
                    salary['employee_number'],
                    salary['iban_number'] or "",
                    salary['full_name'],
                    f"{salary['net_salary']:.2f}"
                ])
                total_net += salary['net_salary']

            # إضافة صف الإجمالي
            data.append([
                "الإجمالي",
                "",
                "",
                f"{total_net:.2f}"
            ])

            # حفظ التقرير
            title = f"تقرير صافي الرواتب - {period['month']:02d}/{period['year']}"
            self.save_report_as_pdf(title, data, headers)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

    def generate_bank_account_report(self):
        """إنتاج تقرير حسب الحساب البنكي"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_employees_report(self):
        """إنتاج تقرير الموظفين"""
        try:
            # جلب البيانات من قاعدة البيانات
            employees = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.iban_number, e.full_name, e.gender, e.marital_status,
                       jt.name as job_title, jg.name as job_grade, c.name as certificate,
                       s.name as stage, e.hire_date, e.status
                FROM employees e
                LEFT JOIN job_titles jt ON e.job_title_id = jt.id
                LEFT JOIN job_grades jg ON e.job_grade_id = jg.id
                LEFT JOIN certificates c ON e.certificate_id = c.id
                LEFT JOIN stages s ON e.stage_id = s.id
                ORDER BY e.employee_number
            """)

            # تحضير البيانات للتقرير
            headers = [
                "الرقم الوظيفي", "رقم الآيبان", "الاسم الرباعي", "الجنس", "الحالة الزوجية",
                "العنوان الوظيفي", "الدرجة الوظيفية", "الشهادة", "المرحلة", "تاريخ التعيين", "الحالة"
            ]
            data = []

            for emp in employees:
                data.append([
                    emp['employee_number'],
                    emp['iban_number'] or "",
                    emp['full_name'],
                    emp['gender'] or "",
                    emp['marital_status'] or "",
                    emp['job_title'] or "",
                    emp['job_grade'] or "",
                    emp['certificate'] or "",
                    emp['stage'] or "",
                    emp['hire_date'] or "",
                    emp['status']
                ])

            # حفظ التقرير
            title = f"تقرير الموظفين - {datetime.now().strftime('%Y-%m-%d')}"
            self.save_report_as_pdf(title, data, headers)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

    # تقارير المصروفات
    def generate_monthly_expenses_report(self):
        """إنتاج تقرير المصروفات الشهري"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_expenses_by_category_report(self):
        """إنتاج تقرير المصروفات حسب الفئة"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_annual_expenses_report(self):
        """إنتاج تقرير المصروفات السنوي"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
