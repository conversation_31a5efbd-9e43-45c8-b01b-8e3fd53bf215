# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import os
from pathlib import Path
from .users_management import UsersManagement
from .settings_management import SettingsManagement
from .employees_management import EmployeesManagement
from .salaries_management import SalariesManagement
from .reports_management import ReportsManagement
from .expenses_management import ExpensesManagement

class MainWindow:
    """فئة النافذة الرئيسية"""
    
    def __init__(self, db_manager, config):
        """تهيئة النافذة الرئيسية"""
        self.db_manager = db_manager
        self.config = config
        self.root = None
        self.current_user = None
        self.current_frame = None
        
        # إعداد الألوان والخطوط
        self.setup_theme()
        
    def setup_theme(self):
        """إعداد الثيم والألوان"""
        # تعيين الثيم
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # الألوان المخصصة
        self.colors = {
            "primary": "#2E86AB",
            "secondary": "#A23B72",
            "success": "#28a745",
            "warning": "#ffc107",
            "danger": "#dc3545",
            "background": "#f8f9fa",
            "surface": "#ffffff",
            "text": "#212529",
            "text_muted": "#6c757d"
        }
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.root = ctk.CTk()
        self.root.title(self.config.window_title)
        self.root.geometry(f"{self.config.window_width}x{self.config.window_height}")
        self.root.minsize(self.config.window_min_width, self.config.window_min_height)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الشبكة
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # إنشاء المنطقة الرئيسية
        self.create_main_area()

        # إنشاء الشريط الجانبي
        self.create_sidebar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # عرض الصفحة الرئيسية
        self.show_dashboard()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        # إطار الشريط الجانبي
        self.sidebar_frame = ctk.CTkFrame(self.root, width=250, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=1, sticky="nsew", padx=0, pady=0)
        self.sidebar_frame.grid_rowconfigure(8, weight=1)
        
        # شعار التطبيق
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="نظام إدارة الرواتب",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # أزرار القائمة الرئيسية
        self.create_menu_buttons()
    
    def create_menu_buttons(self):
        """إنشاء أزرار القائمة"""
        menu_items = [
            ("🏠 الصفحة الرئيسية", self.show_dashboard),
            ("👥 إدارة المستخدمين", self.show_users_management),
            ("⚙️ الإعدادات والتهيئة", self.show_settings),
            ("🧑‍💼 شؤون الموظفين", self.show_employees),
            ("💼 الحسابات", self.show_accounts),
            ("💰 الرواتب", self.show_salaries),
            ("💳 المصروفات", self.show_expenses),
            ("📊 التقارير", self.show_reports),
            ("ℹ️ حول البرنامج", self.show_about)
        ]
        
        self.menu_buttons = []
        for i, (text, command) in enumerate(menu_items, 1):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=command,
                height=40,
                font=ctk.CTkFont(size=14),
                anchor="w"
            )
            btn.grid(row=i, column=0, padx=20, pady=5, sticky="ew")
            self.menu_buttons.append(btn)
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        self.main_frame = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ctk.CTkFrame(self.root, height=30, corner_radius=0)
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=0, pady=0)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="جاهز",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # معلومات المستخدم
        self.user_label = ctk.CTkLabel(
            self.status_frame,
            text="غير مسجل الدخول",
            font=ctk.CTkFont(size=12)
        )
        self.user_label.pack(side="right", padx=10, pady=5)
    
    def clear_main_area(self):
        """مسح المنطقة الرئيسية"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.clear_main_area()
        
        # عنوان الصفحة
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="الصفحة الرئيسية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # بطاقات الإحصائيات
        self.create_dashboard_cards()
        
        self.update_status("الصفحة الرئيسية")
    
    def create_dashboard_cards(self):
        """إنشاء بطاقات الإحصائيات"""
        cards_frame = ctk.CTkFrame(self.main_frame)
        cards_frame.pack(pady=20, padx=20, fill="x")
        
        # بطاقة الموظفين
        employees_card = ctk.CTkFrame(cards_frame)
        employees_card.pack(side="left", padx=10, pady=10, fill="both", expand=True)
        
        ctk.CTkLabel(
            employees_card,
            text="👥",
            font=ctk.CTkFont(size=30)
        ).pack(pady=10)
        
        ctk.CTkLabel(
            employees_card,
            text="إجمالي الموظفين",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack()
        
        # جلب عدد الموظفين من قاعدة البيانات
        try:
            count = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM employees")
            employee_count = count['count'] if count else 0
        except:
            employee_count = 0
            
        ctk.CTkLabel(
            employees_card,
            text=str(employee_count),
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(pady=5)
        
        # بطاقة الرواتب
        salaries_card = ctk.CTkFrame(cards_frame)
        salaries_card.pack(side="left", padx=10, pady=10, fill="both", expand=True)
        
        ctk.CTkLabel(
            salaries_card,
            text="💰",
            font=ctk.CTkFont(size=30)
        ).pack(pady=10)
        
        ctk.CTkLabel(
            salaries_card,
            text="الرواتب المعالجة",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack()
        
        try:
            count = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM employee_salaries WHERE is_processed = 1")
            salary_count = count['count'] if count else 0
        except:
            salary_count = 0
            
        ctk.CTkLabel(
            salaries_card,
            text=str(salary_count),
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(pady=5)
    
    def show_users_management(self):
        """عرض إدارة المستخدمين"""
        self.clear_main_area()

        # إنشاء واجهة إدارة المستخدمين
        users_mgmt = UsersManagement(self.main_frame, self.db_manager)

        self.update_status("إدارة المستخدمين")
    
    def show_settings(self):
        """عرض الإعدادات"""
        self.clear_main_area()

        # إنشاء واجهة الإعدادات والتهيئة
        settings_mgmt = SettingsManagement(self.main_frame, self.db_manager)

        self.update_status("الإعدادات والتهيئة")
    
    def show_employees(self):
        """عرض شؤون الموظفين"""
        self.clear_main_area()

        # إنشاء واجهة شؤون الموظفين
        employees_mgmt = EmployeesManagement(self.main_frame, self.db_manager)

        self.update_status("شؤون الموظفين")
    
    def show_accounts(self):
        """عرض الحسابات"""
        self.clear_main_area()
        
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="الحسابات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(pady=50)
        
        self.update_status("الحسابات")
    
    def show_salaries(self):
        """عرض الرواتب"""
        self.clear_main_area()

        # إنشاء واجهة إدارة الرواتب
        salaries_mgmt = SalariesManagement(self.main_frame, self.db_manager)

        self.update_status("الرواتب")

    def show_expenses(self):
        """عرض المصروفات"""
        self.clear_main_area()

        # إنشاء واجهة إدارة المصروفات
        expenses_mgmt = ExpensesManagement(self.main_frame, self.db_manager)

        self.update_status("المصروفات")
    
    def show_reports(self):
        """عرض التقارير"""
        self.clear_main_area()

        # إنشاء واجهة إدارة التقارير
        reports_mgmt = ReportsManagement(self.main_frame, self.db_manager)

        self.update_status("التقارير")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        self.clear_main_area()
        
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="حول البرنامج",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # معلومات البرنامج
        info_frame = ctk.CTkFrame(self.main_frame)
        info_frame.pack(pady=20, padx=50, fill="both", expand=True)
        
        info_text = f"""
        {self.config.app_name}
        الإصدار: {self.config.app_version}
        المطور: {self.config.app_developer}
        
        نظام محاسبي متكامل لإدارة رواتب الموظفين
        مصمم خصيصاً للمؤسسات العراقية
        
        جميع الحقوق محفوظة © 2025
        """
        
        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=14),
            justify="center"
        )
        info_label.pack(pady=50)
        
        self.update_status("حول البرنامج")
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
    
    def run(self):
        """تشغيل التطبيق"""
        self.create_main_window()
        self.root.mainloop()
