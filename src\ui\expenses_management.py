# -*- coding: utf-8 -*-
"""
واجهة إدارة المصروفات
Expenses Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime
from tkcalendar import DateEntry

class ExpensesManagement:
    """فئة إدارة المصروفات"""
    
    def __init__(self, parent_frame, db_manager):
        """تهيئة واجهة إدارة المصروفات"""
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.main_frame = None
        self.current_view = "add_expense"
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المصروفات"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.parent_frame,
            text="إدارة المصروفات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # أزرار التبديل
        self.create_tab_buttons()
        
        # المنطقة الرئيسية
        self.main_frame = ctk.CTkFrame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # عرض إضافة المصروفات افتراضياً
        self.show_add_expense()
    
    def create_tab_buttons(self):
        """إنشاء أزرار التبديل بين الأقسام"""
        tab_frame = ctk.CTkFrame(self.parent_frame)
        tab_frame.pack(pady=10)
        
        self.add_expense_btn = ctk.CTkButton(
            tab_frame,
            text="إضافة مصروف",
            command=self.show_add_expense,
            width=120
        )
        self.add_expense_btn.pack(side="left", padx=3)
        
        self.view_expenses_btn = ctk.CTkButton(
            tab_frame,
            text="عرض المصروفات",
            command=self.show_view_expenses,
            width=120
        )
        self.view_expenses_btn.pack(side="left", padx=3)
        
        self.categories_btn = ctk.CTkButton(
            tab_frame,
            text="فئات المصروفات",
            command=self.show_expense_categories,
            width=120
        )
        self.categories_btn.pack(side="left", padx=3)
        
        self.reports_btn = ctk.CTkButton(
            tab_frame,
            text="تقارير المصروفات",
            command=self.show_expense_reports,
            width=120
        )
        self.reports_btn.pack(side="left", padx=3)
    
    def show_add_expense(self):
        """عرض إضافة مصروف جديد"""
        self.current_view = "add_expense"
        self.clear_main_frame()
        self.highlight_button(self.add_expense_btn)
        
        # عنوان القسم
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="إضافة مصروف جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        # إطار النموذج
        form_frame = ctk.CTkFrame(self.main_frame)
        form_frame.pack(pady=20, padx=50, fill="both", expand=True)
        
        # رقم المصروف
        expense_number_frame = ctk.CTkFrame(form_frame)
        expense_number_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(expense_number_frame, text="رقم المصروف:", width=150).pack(side="left", padx=5)
        self.expense_number_entry = ctk.CTkEntry(expense_number_frame, width=300)
        self.expense_number_entry.pack(side="right", padx=5)
        
        # تاريخ المصروف
        expense_date_frame = ctk.CTkFrame(form_frame)
        expense_date_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(expense_date_frame, text="تاريخ المصروف:", width=150).pack(side="left", padx=5)
        self.expense_date_entry = DateEntry(
            expense_date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd'
        )
        self.expense_date_entry.pack(side="right", padx=5)
        
        # وصف المصروف
        description_frame = ctk.CTkFrame(form_frame)
        description_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(description_frame, text="وصف المصروف:", width=150).pack(side="left", padx=5)
        self.description_entry = ctk.CTkEntry(description_frame, width=300)
        self.description_entry.pack(side="right", padx=5)
        
        # المبلغ
        amount_frame = ctk.CTkFrame(form_frame)
        amount_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(amount_frame, text="المبلغ:", width=150).pack(side="left", padx=5)
        self.amount_entry = ctk.CTkEntry(amount_frame, width=300)
        self.amount_entry.pack(side="right", padx=5)
        
        # فئة المصروف
        category_frame = ctk.CTkFrame(form_frame)
        category_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(category_frame, text="فئة المصروف:", width=150).pack(side="left", padx=5)
        self.category_combo = ctk.CTkComboBox(
            category_frame,
            values=self.get_expense_categories(),
            width=300
        )
        self.category_combo.pack(side="right", padx=5)
        
        # الحساب البنكي
        bank_account_frame = ctk.CTkFrame(form_frame)
        bank_account_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(bank_account_frame, text="الحساب البنكي:", width=150).pack(side="left", padx=5)
        self.bank_account_combo = ctk.CTkComboBox(
            bank_account_frame,
            values=self.get_bank_accounts(),
            width=300
        )
        self.bank_account_combo.pack(side="right", padx=5)
        
        # الحساب المحاسبي
        account_frame = ctk.CTkFrame(form_frame)
        account_frame.pack(fill="x", pady=10, padx=20)
        
        ctk.CTkLabel(account_frame, text="الحساب المحاسبي:", width=150).pack(side="left", padx=5)
        self.account_combo = ctk.CTkComboBox(
            account_frame,
            values=self.get_chart_accounts(),
            width=300
        )
        self.account_combo.pack(side="right", padx=5)
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(form_frame)
        buttons_frame.pack(pady=30)
        
        ctk.CTkButton(
            buttons_frame,
            text="حفظ المصروف",
            command=self.save_expense,
            width=150,
            height=40
        ).pack(side="left", padx=10)
        
        ctk.CTkButton(
            buttons_frame,
            text="مسح الحقول",
            command=self.clear_form,
            width=150,
            height=40
        ).pack(side="left", padx=10)
        
        # تعيين رقم المصروف التلقائي
        self.set_auto_expense_number()
    
    def get_expense_categories(self):
        """الحصول على فئات المصروفات"""
        categories = [
            "مصروفات إدارية",
            "مصروفات تشغيلية", 
            "مصروفات صيانة",
            "مصروفات سفر",
            "مصروفات اتصالات",
            "مصروفات مكتبية",
            "مصروفات وقود",
            "مصروفات أخرى"
        ]
        return categories
    
    def get_bank_accounts(self):
        """الحصول على الحسابات البنكية"""
        try:
            accounts = self.db_manager.fetch_all("""
                SELECT account_name FROM bank_accounts WHERE is_active = 1
            """)
            return [acc['account_name'] for acc in accounts]
        except:
            return ["حساب افتراضي"]
    
    def get_chart_accounts(self):
        """الحصول على دليل الحسابات"""
        try:
            accounts = self.db_manager.fetch_all("""
                SELECT account_name FROM chart_of_accounts WHERE is_active = 1
            """)
            return [acc['account_name'] for acc in accounts]
        except:
            return ["حساب افتراضي"]
    
    def set_auto_expense_number(self):
        """تعيين رقم المصروف التلقائي"""
        try:
            # جلب آخر رقم مصروف
            last_expense = self.db_manager.fetch_one("""
                SELECT expense_number FROM expenses ORDER BY id DESC LIMIT 1
            """)
            
            if last_expense:
                # استخراج الرقم وزيادته
                last_number = int(last_expense['expense_number'].split('-')[-1])
                new_number = f"EXP-{last_number + 1:06d}"
            else:
                new_number = "EXP-000001"
            
            self.expense_number_entry.delete(0, "end")
            self.expense_number_entry.insert(0, new_number)
            
        except:
            # في حالة الخطأ، استخدم رقم افتراضي
            current_date = datetime.now().strftime("%Y%m%d")
            self.expense_number_entry.delete(0, "end")
            self.expense_number_entry.insert(0, f"EXP-{current_date}-001")
    
    def save_expense(self):
        """حفظ المصروف"""
        try:
            # جلب البيانات من النموذج
            expense_number = self.expense_number_entry.get().strip()
            expense_date = self.expense_date_entry.get_date()
            description = self.description_entry.get().strip()
            amount = float(self.amount_entry.get().strip())
            category = self.category_combo.get()
            bank_account = self.bank_account_combo.get()
            account = self.account_combo.get()
            
            # التحقق من البيانات المطلوبة
            if not expense_number or not description or amount <= 0:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة بشكل صحيح")
                return
            
            # جلب معرفات الحسابات
            bank_account_id = None
            if bank_account:
                bank_acc = self.db_manager.fetch_one("""
                    SELECT id FROM bank_accounts WHERE account_name = ?
                """, (bank_account,))
                if bank_acc:
                    bank_account_id = bank_acc['id']
            
            account_id = None
            if account:
                acc = self.db_manager.fetch_one("""
                    SELECT id FROM chart_of_accounts WHERE account_name = ?
                """, (account,))
                if acc:
                    account_id = acc['id']
            
            # حفظ المصروف في قاعدة البيانات
            self.db_manager.execute_query("""
                INSERT INTO expenses 
                (expense_number, expense_date, description, amount, expense_category, 
                 bank_account_id, account_id, is_approved, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (expense_number, expense_date, description, amount, category,
                  bank_account_id, account_id, False, 1))  # created_by = 1 (افتراضي)
            
            messagebox.showinfo("نجح", "تم حفظ المصروف بنجاح")
            
            # مسح النموذج وتعيين رقم جديد
            self.clear_form()
            self.set_auto_expense_number()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المصروف:\n{str(e)}")
    
    def clear_form(self):
        """مسح حقول النموذج"""
        self.expense_number_entry.delete(0, "end")
        self.description_entry.delete(0, "end")
        self.amount_entry.delete(0, "end")
        self.category_combo.set("")
        self.bank_account_combo.set("")
        self.account_combo.set("")
        self.expense_date_entry.set_date(datetime.now().date())
    
    def show_view_expenses(self):
        """عرض المصروفات"""
        self.current_view = "view_expenses"
        self.clear_main_frame()
        self.highlight_button(self.view_expenses_btn)
        
        # عنوان القسم
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="عرض المصروفات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        ctk.CTkButton(
            buttons_frame,
            text="تحديث القائمة",
            command=self.load_expenses_data,
            width=120
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="تعديل المصروف",
            command=self.edit_expense,
            width=120
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="حذف المصروف",
            command=self.delete_expense,
            width=120
        ).pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="اعتماد المصروف",
            command=self.approve_expense,
            width=120
        ).pack(side="left", padx=5)
        
        # جدول المصروفات
        self.create_expenses_table()
        self.load_expenses_data()
    
    def create_expenses_table(self):
        """إنشاء جدول المصروفات"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)
        
        columns = ("رقم المصروف", "التاريخ", "الوصف", "المبلغ", "الفئة", "الحالة")
        self.expenses_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.expenses_tree.heading(col, text=col)
            self.expenses_tree.column(col, width=120, anchor="center")
        
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.expenses_tree.yview)
        self.expenses_tree.configure(yscrollcommand=scrollbar.set)
        
        self.expenses_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def load_expenses_data(self):
        """تحميل بيانات المصروفات"""
        try:
            # مسح البيانات السابقة
            for item in self.expenses_tree.get_children():
                self.expenses_tree.delete(item)
            
            # جلب المصروفات من قاعدة البيانات
            expenses = self.db_manager.fetch_all("""
                SELECT expense_number, expense_date, description, amount, expense_category, is_approved
                FROM expenses
                ORDER BY expense_date DESC
            """)
            
            # إدراج البيانات في الجدول
            for expense in expenses:
                status = "معتمد" if expense['is_approved'] else "غير معتمد"
                
                self.expenses_tree.insert("", "end", values=(
                    expense['expense_number'],
                    expense['expense_date'],
                    expense['description'],
                    f"{expense['amount']:.2f}",
                    expense['expense_category'] or "",
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المصروفات:\n{str(e)}")
    
    def edit_expense(self):
        """تعديل المصروف"""
        selected = self.expenses_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مصروف للتعديل")
            return
        
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def delete_expense(self):
        """حذف المصروف"""
        selected = self.expenses_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مصروف للحذف")
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المصروف؟")
        if not result:
            return
        
        try:
            # جلب رقم المصروف
            item = self.expenses_tree.item(selected[0])
            expense_number = item['values'][0]
            
            # حذف المصروف من قاعدة البيانات
            self.db_manager.execute_query("""
                DELETE FROM expenses WHERE expense_number = ?
            """, (expense_number,))
            
            messagebox.showinfo("نجح", "تم حذف المصروف بنجاح")
            self.load_expenses_data()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المصروف:\n{str(e)}")
    
    def approve_expense(self):
        """اعتماد المصروف"""
        selected = self.expenses_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مصروف للاعتماد")
            return
        
        try:
            # جلب رقم المصروف
            item = self.expenses_tree.item(selected[0])
            expense_number = item['values'][0]
            
            # اعتماد المصروف
            self.db_manager.execute_query("""
                UPDATE expenses SET is_approved = 1 WHERE expense_number = ?
            """, (expense_number,))
            
            messagebox.showinfo("نجح", "تم اعتماد المصروف بنجاح")
            self.load_expenses_data()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اعتماد المصروف:\n{str(e)}")
    
    def show_expense_categories(self):
        """عرض فئات المصروفات"""
        self.current_view = "expense_categories"
        self.clear_main_frame()
        self.highlight_button(self.categories_btn)
        
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="فئات المصروفات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(pady=50)
    
    def show_expense_reports(self):
        """عرض تقارير المصروفات"""
        self.current_view = "expense_reports"
        self.clear_main_frame()
        self.highlight_button(self.reports_btn)
        
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="تقارير المصروفات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(pady=50)
    
    def highlight_button(self, active_button):
        """تمييز الزر النشط"""
        buttons = [self.add_expense_btn, self.view_expenses_btn, self.categories_btn, self.reports_btn]
        
        for btn in buttons:
            if btn == active_button:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))
    
    def clear_main_frame(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
