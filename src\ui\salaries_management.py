# -*- coding: utf-8 -*-
"""
واجهة إدارة الرواتب
Salaries Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime
import calendar

class SalariesManagement:
    """فئة إدارة الرواتب"""

    def __init__(self, parent_frame, db_manager):
        """تهيئة واجهة إدارة الرواتب"""
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.main_frame = None
        self.current_view = "salary_calculation"

        self.create_interface()

    def create_interface(self):
        """إنشاء واجهة إدارة الرواتب"""
        # مسح المحتوى السابق
        for widget in self.parent_frame.winfo_children():
            widget.destroy()

        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.parent_frame,
            text="إدارة الرواتب",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)

        # أزرار التبديل
        self.create_tab_buttons()

        # المنطقة الرئيسية
        self.main_frame = ctk.CTkFrame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # عرض حساب الرواتب افتراضياً
        self.show_salary_calculation()

    def create_tab_buttons(self):
        """إنشاء أزرار التبديل بين الأقسام"""
        tab_frame = ctk.CTkFrame(self.parent_frame)
        tab_frame.pack(pady=10)

        self.calculation_btn = ctk.CTkButton(
            tab_frame,
            text="حساب الرواتب",
            command=self.show_salary_calculation,
            width=120
        )
        self.calculation_btn.pack(side="left", padx=3)

        self.review_btn = ctk.CTkButton(
            tab_frame,
            text="مراجعة الرواتب",
            command=self.show_salary_review,
            width=120
        )
        self.review_btn.pack(side="left", padx=3)

        self.reports_btn = ctk.CTkButton(
            tab_frame,
            text="تقارير الرواتب",
            command=self.show_salary_reports,
            width=120
        )
        self.reports_btn.pack(side="left", padx=3)

        self.settings_btn = ctk.CTkButton(
            tab_frame,
            text="إعدادات الرواتب",
            command=self.show_salary_settings,
            width=120
        )
        self.settings_btn.pack(side="left", padx=3)

    def show_salary_calculation(self):
        """عرض حساب الرواتب"""
        self.current_view = "salary_calculation"
        self.clear_main_frame()
        self.highlight_button(self.calculation_btn)

        # عنوان القسم
        section_title = ctk.CTkLabel(
            self.main_frame,
            text="حساب الرواتب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # إطار اختيار الفترة
        period_frame = ctk.CTkFrame(self.main_frame)
        period_frame.pack(pady=10, fill="x")

        ctk.CTkLabel(period_frame, text="اختر الشهر والسنة:", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=10)

        # إطار الاختيار
        selection_frame = ctk.CTkFrame(period_frame)
        selection_frame.pack(pady=10)

        # اختيار الشهر
        ctk.CTkLabel(selection_frame, text="الشهر:").pack(side="left", padx=5)
        self.month_combo = ctk.CTkComboBox(
            selection_frame,
            values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
            width=150
        )
        self.month_combo.pack(side="left", padx=5)
        self.month_combo.set(f"{datetime.now().month:02d} - {calendar.month_name[datetime.now().month]}")

        # اختيار السنة
        ctk.CTkLabel(selection_frame, text="السنة:").pack(side="left", padx=5)
        current_year = datetime.now().year
        self.year_combo = ctk.CTkComboBox(
            selection_frame,
            values=[str(year) for year in range(current_year-5, current_year+5)],
            width=100
        )
        self.year_combo.pack(side="left", padx=5)
        self.year_combo.set(str(current_year))

        # زر تحميل الموظفين
        ctk.CTkButton(
            selection_frame,
            text="تحميل الموظفين",
            command=self.load_employees_for_salary,
            width=120
        ).pack(side="left", padx=10)

        # أزرار العمليات
        operations_frame = ctk.CTkFrame(self.main_frame)
        operations_frame.pack(pady=10, fill="x")

        ctk.CTkButton(
            operations_frame,
            text="حساب راتب موظف",
            command=self.calculate_single_salary,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            operations_frame,
            text="حساب جميع الرواتب",
            command=self.calculate_all_salaries,
            width=150
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            operations_frame,
            text="حفظ الرواتب",
            command=self.save_salaries,
            width=150
        ).pack(side="left", padx=5)

        # جدول الموظفين والرواتب
        self.create_salary_table()

    def create_salary_table(self):
        """إنشاء جدول الرواتب"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        # إنشاء Treeview مع شريط تمرير أفقي
        columns = (
            "الرقم الوظيفي", "الاسم الرباعي", "الراتب الأساسي", "إجمالي المخصصات",
            "إجمالي الاستقطاعات", "صافي الراتب", "الحالة"
        )

        self.salary_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.salary_tree.heading(col, text=col)
            self.salary_tree.column(col, width=120, anchor="center")

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.salary_tree.yview)
        self.salary_tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.salary_tree.xview)
        self.salary_tree.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.salary_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # إعداد الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def load_employees_for_salary(self):
        """تحميل الموظفين لحساب الرواتب"""
        try:
            # مسح البيانات السابقة
            for item in self.salary_tree.get_children():
                self.salary_tree.delete(item)

            # جلب الموظفين النشطين
            employees = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.full_name, e.iban_number
                FROM employees e
                WHERE e.status = 'مستمر'
                ORDER BY e.employee_number
            """)

            # إدراج الموظفين في الجدول
            for emp in employees:
                self.salary_tree.insert("", "end", values=(
                    emp['employee_number'],
                    emp['full_name'],
                    "0.00",  # الراتب الأساسي
                    "0.00",  # إجمالي المخصصات
                    "0.00",  # إجمالي الاستقطاعات
                    "0.00",  # صافي الراتب
                    "لم يحسب"  # الحالة
                ))

            messagebox.showinfo("نجح", f"تم تحميل {len(employees)} موظف")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموظفين:\n{str(e)}")

    def calculate_single_salary(self):
        """حساب راتب موظف واحد"""
        selected = self.salary_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف لحساب راتبه")
            return

        # جلب بيانات الموظف المحدد
        item = self.salary_tree.item(selected[0])
        employee_number = item['values'][0]

        # فتح نافذة حساب الراتب
        self.open_salary_calculation_dialog(employee_number)

    def open_salary_calculation_dialog(self, employee_number):
        """فتح نافذة حساب الراتب"""
        dialog = ctk.CTkToplevel()
        dialog.title(f"حساب راتب الموظف - {employee_number}")
        dialog.geometry("800x900")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (dialog.winfo_screenheight() // 2) - (900 // 2)
        dialog.geometry(f"800x900+{x}+{y}")

        # العنوان
        ctk.CTkLabel(
            dialog,
            text=f"حساب راتب الموظف - {employee_number}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(dialog, width=750, height=750)
        scroll_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # جلب بيانات الموظف
        employee = self.db_manager.fetch_one("""
            SELECT e.*, jt.name as job_title, jg.name as job_grade,
                   c.name as certificate, s.name as stage
            FROM employees e
            LEFT JOIN job_titles jt ON e.job_title_id = jt.id
            LEFT JOIN job_grades jg ON e.job_grade_id = jg.id
            LEFT JOIN certificates c ON e.certificate_id = c.id
            LEFT JOIN stages s ON e.stage_id = s.id
            WHERE e.employee_number = ?
        """, (employee_number,))

        if not employee:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات الموظف")
            dialog.destroy()
            return

        # عرض بيانات الموظف
        self.create_employee_info_section(scroll_frame, employee)

        # قسم مكونات الراتب
        self.create_salary_components_section(scroll_frame)

        # قسم المخصصات
        self.create_allowances_section(scroll_frame)

        # قسم الاستقطاعات
        self.create_deductions_section(scroll_frame)

        # قسم الصافي
        self.create_net_salary_section(scroll_frame)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(pady=20)

        ctk.CTkButton(
            buttons_frame,
            text="حساب الراتب",
            command=lambda: self.calculate_salary_details(employee_number),
            width=120
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=lambda: self.save_single_salary(employee_number, dialog),
            width=120
        ).pack(side="left", padx=5)

        ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=120
        ).pack(side="left", padx=5)

        # حفظ مرجع النافذة
        self.salary_dialog = dialog

    def create_employee_info_section(self, parent, employee):
        """إنشاء قسم معلومات الموظف"""
        info_frame = ctk.CTkFrame(parent)
        info_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            info_frame,
            text="معلومات الموظف",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        # معلومات الموظف في صفوف
        info_text = f"""
        الرقم الوظيفي: {employee['employee_number']}
        رقم الآيبان: {employee['iban_number'] or 'غير محدد'}
        الاسم الرباعي: {employee['full_name']}
        العنوان الوظيفي: {employee['job_title'] or 'غير محدد'}
        الدرجة الوظيفية: {employee['job_grade'] or 'غير محدد'}
        الشهادة: {employee['certificate'] or 'غير محدد'}
        المرحلة: {employee['stage'] or 'غير محدد'}
        """

        ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="right"
        ).pack(pady=10)

    def create_salary_components_section(self, parent):
        """إنشاء قسم مكونات الراتب"""
        components_frame = ctk.CTkFrame(parent)
        components_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            components_frame,
            text="مكونات الراتب",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        # الراتب الأساسي
        basic_frame = ctk.CTkFrame(components_frame)
        basic_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(basic_frame, text="الراتب الأساسي:").pack(side="left", padx=5)
        self.basic_salary_entry = ctk.CTkEntry(basic_frame, width=150)
        self.basic_salary_entry.pack(side="right", padx=5)

        # حصة الدائرة في المساهمة الحكومية
        gov_contribution_frame = ctk.CTkFrame(components_frame)
        gov_contribution_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(gov_contribution_frame, text="حصة الدائرة في المساهمة الحكومية (15%):").pack(side="left", padx=5)
        self.gov_contribution_entry = ctk.CTkEntry(gov_contribution_frame, width=150)
        self.gov_contribution_entry.pack(side="right", padx=5)

        # فرق راتب
        salary_diff_frame = ctk.CTkFrame(components_frame)
        salary_diff_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(salary_diff_frame, text="فرق راتب:").pack(side="left", padx=5)
        self.salary_diff_entry = ctk.CTkEntry(salary_diff_frame, width=150)
        self.salary_diff_entry.pack(side="right", padx=5)

    def create_allowances_section(self, parent):
        """إنشاء قسم المخصصات"""
        allowances_frame = ctk.CTkFrame(parent)
        allowances_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            allowances_frame,
            text="المخصصات",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        # قاموس لحفظ حقول المخصصات
        self.allowances_entries = {}

        allowances_list = [
            ("position_allowance", "مخصصات المنصب"),
            ("marriage_allowance", "مخصصات الزوجية"),
            ("children_allowance", "مخصصات الأولاد"),
            ("engineering_allowance", "مخصصات هندسية"),
            ("certificate_allowance", "مخصصات الشهادة"),
            ("craft_allowance", "مخصصات الحرفة"),
            ("danger_allowance", "مخصصات الخطورة"),
            ("location_allowance", "مخصصات الموقع الجغرافي")
        ]

        for key, label in allowances_list:
            frame = ctk.CTkFrame(allowances_frame)
            frame.pack(fill="x", pady=2, padx=10)

            ctk.CTkLabel(frame, text=f"{label}:").pack(side="left", padx=5)
            entry = ctk.CTkEntry(frame, width=150)
            entry.pack(side="right", padx=5)
            entry.insert(0, "0.00")
            self.allowances_entries[key] = entry

        # إجمالي المخصصات
        total_allowances_frame = ctk.CTkFrame(allowances_frame)
        total_allowances_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(
            total_allowances_frame,
            text="إجمالي المخصصات:",
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=5)

        self.total_allowances_entry = ctk.CTkEntry(
            total_allowances_frame,
            width=150,
            state="readonly"
        )
        self.total_allowances_entry.pack(side="right", padx=5)

    def create_deductions_section(self, parent):
        """إنشاء قسم الاستقطاعات"""
        deductions_frame = ctk.CTkFrame(parent)
        deductions_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            deductions_frame,
            text="الاستقطاعات",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        # قاموس لحفظ حقول الاستقطاعات
        self.deductions_entries = {}

        deductions_list = [
            ("retirement_fund", "صندوق تقاعد موظفي الدولة (10%)"),
            ("department_contribution", "حصة الدائرة في المساهمة الحكومية (15%)"),
            ("income_tax", "ضريبة الدخل"),
            ("social_protection_fund", "صندوق هيئة الحماية الاجتماعية"),
            ("health_insurance", "أمانات الضمان الصحي"),
            ("other_departments", "دوائر وجهات أخرى"),
            ("salary_difference_deduction", "فرق راتب"),
            ("execution_departments", "دوائر التنفيذ"),
            ("bank_installments", "أقساط المصارف")
        ]

        for key, label in deductions_list:
            frame = ctk.CTkFrame(deductions_frame)
            frame.pack(fill="x", pady=2, padx=10)

            ctk.CTkLabel(frame, text=f"{label}:").pack(side="left", padx=5)
            entry = ctk.CTkEntry(frame, width=150)
            entry.pack(side="right", padx=5)
            entry.insert(0, "0.00")
            self.deductions_entries[key] = entry

        # إجمالي الاستقطاعات
        total_deductions_frame = ctk.CTkFrame(deductions_frame)
        total_deductions_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(
            total_deductions_frame,
            text="إجمالي الاستقطاعات:",
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=5)

        self.total_deductions_entry = ctk.CTkEntry(
            total_deductions_frame,
            width=150,
            state="readonly"
        )
        self.total_deductions_entry.pack(side="right", padx=5)

    def create_net_salary_section(self, parent):
        """إنشاء قسم صافي الراتب"""
        net_frame = ctk.CTkFrame(parent)
        net_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            net_frame,
            text="الصافي",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        # إجمالي الراتب
        gross_salary_frame = ctk.CTkFrame(net_frame)
        gross_salary_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(
            gross_salary_frame,
            text="إجمالي الراتب:",
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=5)

        self.gross_salary_entry = ctk.CTkEntry(
            gross_salary_frame,
            width=150,
            state="readonly"
        )
        self.gross_salary_entry.pack(side="right", padx=5)

        # صافي الراتب
        net_salary_frame = ctk.CTkFrame(net_frame)
        net_salary_frame.pack(fill="x", pady=5, padx=10)

        ctk.CTkLabel(
            net_salary_frame,
            text="صافي الراتب:",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(side="left", padx=5)

        self.net_salary_entry = ctk.CTkEntry(
            net_salary_frame,
            width=150,
            state="readonly",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.net_salary_entry.pack(side="right", padx=5)

    def calculate_salary_details(self, employee_number):
        """حساب تفاصيل الراتب"""
        try:
            # جلب القيم من الحقول
            basic_salary = float(self.basic_salary_entry.get() or 0)
            gov_contribution = float(self.gov_contribution_entry.get() or 0)
            salary_diff = float(self.salary_diff_entry.get() or 0)

            # حساب إجمالي المخصصات
            total_allowances = 0
            for key, entry in self.allowances_entries.items():
                value = float(entry.get() or 0)
                total_allowances += value

            # حساب الاستقطاعات التلقائية
            retirement_fund = basic_salary * 0.10  # 10%
            department_contribution = basic_salary * 0.15  # 15%

            # تحديث حقول الاستقطاعات التلقائية
            self.deductions_entries['retirement_fund'].delete(0, "end")
            self.deductions_entries['retirement_fund'].insert(0, f"{retirement_fund:.2f}")

            self.deductions_entries['department_contribution'].delete(0, "end")
            self.deductions_entries['department_contribution'].insert(0, f"{department_contribution:.2f}")

            # حساب إجمالي الاستقطاعات
            total_deductions = 0
            for key, entry in self.deductions_entries.items():
                value = float(entry.get() or 0)
                total_deductions += value

            # حساب إجمالي الراتب
            gross_salary = basic_salary + total_allowances + gov_contribution + salary_diff

            # حساب صافي الراتب
            net_salary = gross_salary - total_deductions

            # تحديث الحقول
            self.total_allowances_entry.configure(state="normal")
            self.total_allowances_entry.delete(0, "end")
            self.total_allowances_entry.insert(0, f"{total_allowances:.2f}")
            self.total_allowances_entry.configure(state="readonly")

            self.total_deductions_entry.configure(state="normal")
            self.total_deductions_entry.delete(0, "end")
            self.total_deductions_entry.insert(0, f"{total_deductions:.2f}")
            self.total_deductions_entry.configure(state="readonly")

            self.gross_salary_entry.configure(state="normal")
            self.gross_salary_entry.delete(0, "end")
            self.gross_salary_entry.insert(0, f"{gross_salary:.2f}")
            self.gross_salary_entry.configure(state="readonly")

            self.net_salary_entry.configure(state="normal")
            self.net_salary_entry.delete(0, "end")
            self.net_salary_entry.insert(0, f"{net_salary:.2f}")
            self.net_salary_entry.configure(state="readonly")

            messagebox.showinfo("نجح", "تم حساب الراتب بنجاح")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة في جميع الحقول")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حساب الراتب:\n{str(e)}")

    def save_single_salary(self, employee_number, dialog):
        """حفظ راتب موظف واحد"""
        try:
            # جلب الشهر والسنة
            month = int(self.month_combo.get().split(" - ")[0])
            year = int(self.year_combo.get())

            # جلب القيم المحسوبة
            basic_salary = float(self.basic_salary_entry.get() or 0)
            gov_contribution = float(self.gov_contribution_entry.get() or 0)
            salary_diff = float(self.salary_diff_entry.get() or 0)

            # جلب المخصصات
            allowances_data = {}
            for key, entry in self.allowances_entries.items():
                allowances_data[key] = float(entry.get() or 0)

            # جلب الاستقطاعات
            deductions_data = {}
            for key, entry in self.deductions_entries.items():
                deductions_data[key] = float(entry.get() or 0)

            # جلب الإجماليات
            total_allowances = float(self.total_allowances_entry.get() or 0)
            total_deductions = float(self.total_deductions_entry.get() or 0)
            net_salary = float(self.net_salary_entry.get() or 0)

            # جلب معرف الموظف
            employee = self.db_manager.fetch_one("""
                SELECT id FROM employees WHERE employee_number = ?
            """, (employee_number,))

            if not employee:
                messagebox.showerror("خطأ", "لم يتم العثور على الموظف")
                return

            # حفظ الراتب في قاعدة البيانات
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO employee_salaries
                (employee_id, salary_month, salary_year, basic_salary, government_contribution,
                 salary_difference, position_allowance, marriage_allowance, children_allowance,
                 engineering_allowance, certificate_allowance, craft_allowance, danger_allowance,
                 location_allowance, total_allowances, retirement_fund, department_contribution,
                 income_tax, social_protection_fund, health_insurance, other_departments,
                 salary_difference_deduction, execution_departments, bank_installments,
                 total_deductions, net_salary, is_processed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                employee['id'], month, year, basic_salary, gov_contribution, salary_diff,
                allowances_data['position_allowance'], allowances_data['marriage_allowance'],
                allowances_data['children_allowance'], allowances_data['engineering_allowance'],
                allowances_data['certificate_allowance'], allowances_data['craft_allowance'],
                allowances_data['danger_allowance'], allowances_data['location_allowance'],
                total_allowances, deductions_data['retirement_fund'],
                deductions_data['department_contribution'], deductions_data['income_tax'],
                deductions_data['social_protection_fund'], deductions_data['health_insurance'],
                deductions_data['other_departments'], deductions_data['salary_difference_deduction'],
                deductions_data['execution_departments'], deductions_data['bank_installments'],
                total_deductions, net_salary, True
            ))

            messagebox.showinfo("نجح", "تم حفظ الراتب بنجاح")
            dialog.destroy()

            # تحديث الجدول الرئيسي
            self.load_employees_for_salary()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الراتب:\n{str(e)}")

    def calculate_all_salaries(self):
        """حساب جميع الرواتب"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def save_salaries(self):
        """حفظ جميع الرواتب"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def show_salary_review(self):
        """عرض مراجعة الرواتب"""
        self.current_view = "salary_review"
        self.clear_main_frame()
        self.highlight_button(self.review_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="مراجعة الرواتب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # إطار اختيار الفترة
        period_frame = ctk.CTkFrame(self.main_frame)
        period_frame.pack(pady=10, fill="x")

        ctk.CTkLabel(period_frame, text="اختر الشهر والسنة للمراجعة:", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=10)

        # إطار الاختيار
        selection_frame = ctk.CTkFrame(period_frame)
        selection_frame.pack(pady=10)

        # اختيار الشهر
        ctk.CTkLabel(selection_frame, text="الشهر:").pack(side="left", padx=5)
        self.review_month_combo = ctk.CTkComboBox(
            selection_frame,
            values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
            width=150
        )
        self.review_month_combo.pack(side="left", padx=5)
        self.review_month_combo.set(f"{datetime.now().month:02d} - {calendar.month_name[datetime.now().month]}")

        # اختيار السنة
        ctk.CTkLabel(selection_frame, text="السنة:").pack(side="left", padx=5)
        current_year = datetime.now().year
        self.review_year_combo = ctk.CTkComboBox(
            selection_frame,
            values=[str(year) for year in range(current_year-5, current_year+5)],
            width=100
        )
        self.review_year_combo.pack(side="left", padx=5)
        self.review_year_combo.set(str(current_year))

        # زر تحميل الرواتب
        ctk.CTkButton(
            selection_frame,
            text="تحميل الرواتب",
            command=self.load_salaries_for_review,
            width=120
        ).pack(side="left", padx=10)

        # جدول مراجعة الرواتب
        self.create_review_table()

    def create_review_table(self):
        """إنشاء جدول مراجعة الرواتب"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, pady=10)

        columns = (
            "الرقم الوظيفي", "الاسم الرباعي", "الراتب الأساسي", "إجمالي المخصصات",
            "إجمالي الاستقطاعات", "صافي الراتب", "تاريخ المعالجة"
        )

        self.review_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.review_tree.heading(col, text=col)
            self.review_tree.column(col, width=120, anchor="center")

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.review_tree.yview)
        self.review_tree.configure(yscrollcommand=scrollbar.set)

        self.review_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_salaries_for_review(self):
        """تحميل الرواتب للمراجعة"""
        try:
            # مسح البيانات السابقة
            for item in self.review_tree.get_children():
                self.review_tree.delete(item)

            # جلب الشهر والسنة
            month = int(self.review_month_combo.get().split(" - ")[0])
            year = int(self.review_year_combo.get())

            # جلب الرواتب المحفوظة
            salaries = self.db_manager.fetch_all("""
                SELECT e.employee_number, e.full_name, s.basic_salary, s.total_allowances,
                       s.total_deductions, s.net_salary, s.created_at
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.salary_month = ? AND s.salary_year = ? AND s.is_processed = 1
                ORDER BY e.employee_number
            """, (month, year))

            # إدراج البيانات في الجدول
            for salary in salaries:
                created_date = salary['created_at'][:10] if salary['created_at'] else ""

                self.review_tree.insert("", "end", values=(
                    salary['employee_number'],
                    salary['full_name'],
                    f"{salary['basic_salary']:.2f}",
                    f"{salary['total_allowances']:.2f}",
                    f"{salary['total_deductions']:.2f}",
                    f"{salary['net_salary']:.2f}",
                    created_date
                ))

            messagebox.showinfo("نجح", f"تم تحميل {len(salaries)} راتب")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الرواتب:\n{str(e)}")

    def show_salary_reports(self):
        """عرض تقارير الرواتب"""
        self.current_view = "salary_reports"
        self.clear_main_frame()
        self.highlight_button(self.reports_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="تقارير الرواتب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        # أزرار التقارير
        reports_frame = ctk.CTkFrame(self.main_frame)
        reports_frame.pack(pady=20, fill="x")

        # الصف الأول من التقارير
        row1_frame = ctk.CTkFrame(reports_frame)
        row1_frame.pack(pady=10)

        ctk.CTkButton(
            row1_frame,
            text="تقرير الرواتب الشهري",
            command=self.generate_monthly_salary_report,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            row1_frame,
            text="تقرير المخصصات",
            command=self.generate_allowances_report,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            row1_frame,
            text="تقرير الاستقطاعات",
            command=self.generate_deductions_report,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        # الصف الثاني من التقارير
        row2_frame = ctk.CTkFrame(reports_frame)
        row2_frame.pack(pady=10)

        ctk.CTkButton(
            row2_frame,
            text="تقرير صافي الرواتب",
            command=self.generate_net_salary_report,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            row2_frame,
            text="تقرير حسب الحساب البنكي",
            command=self.generate_bank_account_report,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            row2_frame,
            text="تقرير مقارن",
            command=self.generate_comparison_report,
            width=180,
            height=40
        ).pack(side="left", padx=10)

        # معلومات التقارير
        info_frame = ctk.CTkFrame(self.main_frame)
        info_frame.pack(pady=20, padx=20, fill="both", expand=True)

        info_text = """
        تقارير الرواتب المتاحة:

        • تقرير الرواتب الشهري: عرض جميع رواتب الموظفين لشهر محدد
        • تقرير المخصصات: تفصيل جميع أنواع المخصصات
        • تقرير الاستقطاعات: تفصيل جميع أنواع الاستقطاعات
        • تقرير صافي الرواتب: عرض صافي الرواتب فقط
        • تقرير حسب الحساب البنكي: تجميع الرواتب حسب الحساب البنكي
        • تقرير مقارن: مقارنة الرواتب بين شهرين مختلفين
        """

        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=14),
            justify="right"
        )
        info_label.pack(pady=30)

    def generate_monthly_salary_report(self):
        """إنتاج تقرير الرواتب الشهري"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_allowances_report(self):
        """إنتاج تقرير المخصصات"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_deductions_report(self):
        """إنتاج تقرير الاستقطاعات"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_net_salary_report(self):
        """إنتاج تقرير صافي الرواتب"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_bank_account_report(self):
        """إنتاج تقرير حسب الحساب البنكي"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def generate_comparison_report(self):
        """إنتاج تقرير مقارن"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")

    def show_salary_settings(self):
        """عرض إعدادات الرواتب"""
        self.current_view = "salary_settings"
        self.clear_main_frame()
        self.highlight_button(self.settings_btn)

        section_title = ctk.CTkLabel(
            self.main_frame,
            text="إعدادات الرواتب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        section_title.pack(pady=10)

        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قيد التطوير...",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(pady=50)

    def highlight_button(self, active_button):
        """تمييز الزر النشط"""
        buttons = [self.calculation_btn, self.review_btn, self.reports_btn, self.settings_btn]

        for btn in buttons:
            if btn == active_button:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))

    def clear_main_frame(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()