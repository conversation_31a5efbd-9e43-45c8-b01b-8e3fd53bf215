# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Configuration
"""

import os
import json
from pathlib import Path

class Config:
    """فئة إدارة إعدادات التطبيق"""
    
    def __init__(self):
        """تهيئة الإعدادات"""
        self.app_name = "نظام إدارة الرواتب اللامركزي"
        self.app_version = "1.0.0"
        self.app_developer = "Augment Agent"
        
        # مسارات الملفات
        self.base_dir = Path(__file__).parent.parent.parent
        self.data_dir = self.base_dir / "data"
        self.assets_dir = self.base_dir / "assets"
        self.reports_dir = self.base_dir / "reports"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        self.data_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)
        
        # قاعدة البيانات
        self.database_path = self.data_dir / "payroll_system.db"
        
        # إعدادات الواجهة
        self.window_title = self.app_name
        self.window_width = 1200
        self.window_height = 800
        self.window_min_width = 1000
        self.window_min_height = 600
        
        # الخطوط
        self.font_family = "Cairo"
        self.font_size = 12
        self.font_weight = "bold"
        
        # الألوان
        self.colors = {
            "primary": "#2E86AB",
            "secondary": "#A23B72", 
            "success": "#F18F01",
            "warning": "#C73E1D",
            "background": "#F5F5F5",
            "surface": "#FFFFFF",
            "text": "#333333",
            "text_secondary": "#666666"
        }
        
        # إعدادات التقارير
        self.report_settings = {
            "page_size": "A4",
            "margin": 20,
            "font_size": 10,
            "header_font_size": 14,
            "title_font_size": 16
        }
        
        # تحميل الإعدادات المخصصة
        self.load_custom_settings()
    
    def load_custom_settings(self):
        """تحميل الإعدادات المخصصة من ملف"""
        config_file = self.data_dir / "config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    custom_settings = json.load(f)
                    # تحديث الإعدادات
                    for key, value in custom_settings.items():
                        if hasattr(self, key):
                            setattr(self, key, value)
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات المخصصة: {e}")
    
    def save_custom_settings(self):
        """حفظ الإعدادات المخصصة"""
        config_file = self.data_dir / "config.json"
        settings = {
            "window_width": self.window_width,
            "window_height": self.window_height,
            "font_size": self.font_size,
            "colors": self.colors
        }
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def get_font_tuple(self, size=None, weight=None):
        """الحصول على tuple الخط للاستخدام في tkinter"""
        return (
            self.font_family,
            size or self.font_size,
            weight or self.font_weight
        )
